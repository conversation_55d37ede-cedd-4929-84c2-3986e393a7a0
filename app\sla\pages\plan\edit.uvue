<template>
  <view class="edit-plan-container">
    <!-- ??????? -->
    <sla-navbar title="????" @back="goBack"></sla-navbar>

    <scroll-view class="form-container" scroll-y v-if="plan">
      <view class="form-section">
        <view class="form-item">
          <text class="form-label required">????</text>
          <input class="form-input" v-model="plan.title" placeholder="???????" />
        </view>

        <view class="form-item">
          <text class="form-label">????</text>
          <view class="plan-type-selector">
            <view
              class="plan-type-item plan-type-item--disabled"
              :class="{'plan-type-item--active': plan.type === 'daily'}"
            >
              <text class="plan-type-item__text">???</text>
            </view>
            <view
              class="plan-type-item plan-type-item--disabled"
              :class="{'plan-type-item--active': plan.type === 'weekly'}"
            >
              <text class="plan-type-item__text">???</text>
            </view>
            <view
              class="plan-type-item plan-type-item--disabled"
              :class="{'plan-type-item--active': plan.type === 'monthly'}"
            >
              <text class="plan-type-item__text">???</text>
            </view>
            <view
              class="plan-type-item plan-type-item--disabled"
              :class="{'plan-type-item--active': plan.type === 'custom'}"
            >
              <text class="plan-type-item__text">???</text>
            </view>
          </view>
        </view>

        <view class="form-item">
          <text class="form-label required">??</text>
          <picker mode="date" :value="plan.date" @change="onDateChange">
            <view class="picker-item">
              <text class="picker-text">{{formatDate(plan.date) || '?????'}}</text>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="form-label">???</text>
          <view class="priority-selector">
            <view
              class="priority-item priority-item--low"
              :class="{'priority-item--active': plan.priority === 'low'}"
              @click="plan.priority = 'low'"
            >
              <text class="priority-item__text">?</text>
            </view>
            <view
              class="priority-item priority-item--medium"
              :class="{'priority-item--active': plan.priority === 'medium'}"
              @click="plan.priority = 'medium'"
            >
              <text class="priority-item__text">?</text>
            </view>
            <view
              class="priority-item priority-item--high"
              :class="{'priority-item--active': plan.priority === 'high'}"
              @click="plan.priority = 'high'"
            >
              <text class="priority-item__text">?</text>
            </view>
          </view>
        </view>

        <view class="form-item">
          <text class="form-label">??</text>
          <textarea class="form-textarea" v-model="plan.description" placeholder="??????????"></textarea>
        </view>
      </view>
    </scroll-view>

    <view class="footer" v-if="plan">
      <view class="footer-buttons">
        <view class="btn-cancel" hover-class="btn-hover" @click="goBack">??</view>
        <view class="btn-primary" hover-class="btn-hover" @click="savePlan">??</view>
      </view>
    </view>
  </view>
</template>

<script>
import planApi from '../../utils/api/plan.js';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';

// 添加子任务接口定义
interface SubTask {
  id: number;
  title: string;
  startTime: string;
  endTime: string;
  description: string;
  completed: boolean;
}

export default {
  components: {
    SlaNavbar
  },
  data() {
    return {
      planId: null as number | null,
      plan: null as any
    }
  },
  onReady() {
    // ?? $pageLayoutInstance ??
    try {
      if (this.$scope && !this.$scope.$pageLayoutInstance) {
        console.log('onReady: 创建$pageLayoutInstance对象');
        this.$scope.$pageLayoutInstance = {};
      }
    } catch (e) {
      console.error('onReady: ??$pageLayoutInstance??:', e);
    }
  },

  onLoad(options) {
    try {
      // 确保隐藏任何加载状态
      try {
        uni.hideLoading();
      } catch (e) {
        console.error('隐藏加载失败', e);
      }

      // 创建 $pageLayoutInstance 对象
      try {
        if (this.$scope && !this.$scope.$pageLayoutInstance) {
          console.log('创建$pageLayoutInstance对象');
          this.$scope.$pageLayoutInstance = {};
        }
      } catch (e) {
        console.error('创建$pageLayoutInstance失败:', e);
      }

      if (options && options.id) {
        this.planId = parseInt(options.id as string);
        this.loadPlanData();
      } else {
        uni.showToast({
          title: '计划ID无效',
          icon: 'none'
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('onLoad????:', error);
      uni.showToast({
        title: '??????',
        icon: 'none'
      });
    }
  },
  methods: {
    loadPlanData() {
      // ??????      uni.showLoading({
        title: '????..'
      });

      // ??API??????
      planApi.getPlanDetail(this.planId).then(res => {
        if (res.code === 200 && res.data) {
          // ????????????          this.plan = {
            id: res.data.planId,
            title: res.data.title,
            type: res.data.type,
            date: res.data.date,
            endDate: res.data.endDate,
            priority: res.data.priority,
            description: res.data.description,
            completed: res.data.completed,
            todayCompleted: res.data.todayCompleted,
            aiGenerated: res.data.aiGenerated,
            createTime: res.data.createTime,
            subTasks: res.data.subTasks ? res.data.subTasks.map(subTask => {
              return {
                id: subTask.subTaskId,
                title: subTask.title,
                startTime: subTask.startTime,
                endTime: subTask.endTime,
                completed: subTask.completed,
                description: subTask.description
              };
            }) : []
          };
          console.log('????????:', this.plan);
        } else {
          console.error('????????:', res.message);
          uni.showToast({
            title: '????????',
            icon: 'none'
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      }).catch(err => {
        console.error('????????:', err);

        // ???????????
        if (err && err.code === 401) {
          // ???????????????????          uni.showToast({
            title: '????????????,
            icon: 'none',
            duration: 2000
          });

          // ??????????????          setTimeout(() => {
            uni.navigateBack();
          }, 2000);
        } else {
          // ????
          uni.showToast({
            title: '??????????',
            icon: 'none'
          });

          // ????
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      }).finally(() => {
        uni.hideLoading();
      });
    },

    goBack() {
      try {
        // ??????????????????        try {
          uni.hideLoading();
        } catch (e) {
          console.error('????????????', e);
        }

        // ??setTimeout??????????        setTimeout(() => {
          // ???????
          const pages = getCurrentPages();
          if (pages.length > 1) {
            // ?????????navigateBack
            uni.navigateBack();
          } else {
            // ??????????switchTab
            uni.switchTab({
              url: '/pages/plan/index'
            });
          }
        }, 100);
      } catch (error) {
        console.error('goBack??????:', error);
        // ?????????switchTab
        uni.switchTab({
          url: '/pages/plan/index'
        });
      }
    },

    onDateChange(e) {
      if (this.plan) {
        this.plan.date = e.detail.value;
        console.log('????????', this.plan.date);
      }
    },

    // ??????    formatDate(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();

      return `${year}??{month}??{day}?`;
    },

    savePlan() {
      // ????
      if (!this.plan || !this.plan.title) {
        uni.showToast({
          title: '????????,
          icon: 'none'
        });
        return;
      }

      if (!this.plan.date) {
        uni.showToast({
          title: '?????',
          icon: 'none'
        });
        return;
      }

      // ??????      uni.showLoading({
        title: '????..'
      });

      // ??????
      const planData = {
        title: this.plan.title,
        type: this.plan.type,
        date: this.plan.date,
        endDate: this.plan.endDate || this.plan.date,
        priority: this.plan.priority || 'medium',
        description: this.plan.description || '',
        subTasks: this.plan.subTasks ? this.plan.subTasks.map(subTask => {
          return {
            subTaskId: subTask.id,
            title: subTask.title,
            startTime: subTask.startTime,
            endTime: subTask.endTime,
            completed: subTask.completed,
            description: subTask.description || ''
          };
        }) : []
      };

      // ??API????
      planApi.updatePlan(this.planId, planData).then(res => {
        if (res.code === 200) {
          // ????????          uni.showToast({
            title: '??????,
            icon: 'success',
            duration: 1500
          });

          // ??????????????          setTimeout(() => {
            try {
              uni.navigateBack({
                fail: (err) => {
                  console.error('????????', err);
                  // ???????????reLaunch
                  uni.reLaunch({
                    url: '/pages/plan/index'
                  });
                }
              });
            } catch (error) {
              console.error('????????', error);
              // ?????????reLaunch
              uni.reLaunch({
                url: '/pages/plan/index'
              });
            }
          }, 1500);
        } else {
          console.error('??????:', res.message);
          uni.showToast({
            title: res.message || '??????',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('??????:', err);

        // ???????????
        if (err && err.code === 401) {
          // ????????????
          uni.showToast({
            title: '????????????,
            icon: 'none',
            duration: 2000
          });
        } else {
          // ????
          uni.showToast({
            title: '??????????',
            icon: 'none'
          });
        }
      }).finally(() => {
        uni.hideLoading();
      });
    }
  }
}
</script>

<style>
.edit-plan-container {
  display: flex;
  flex-direction: column;
  min-height: 800px;
  background-image: linear-gradient(135deg, #eef2ff, #f5f7fa);
  position: relative;
}

.header {
  height: 56px;
  background: rgba(255, 255, 255, 0.9);

  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 2px 16px rgba(31, 60, 136, 0.06);
  border-bottom: 1px solid rgba(91, 127, 255, 0.08);
}

.back-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: transform 0.2s ease;
}

.back-button:active {
  transform: scale(0.92);
}

.back-icon {
  font-size: 22px;
  color: #5B7FFF;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #2E3A59;
  letter-spacing: 0.5px;
}

.placeholder {
  width: 40px;
}

.form-container {
  flex: 1;
  padding: 16px;

}


  to { opacity: 1; transform: translateY(0); }
}

.form-section {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 6px 20px rgba(31, 60, 136, 0.05);
  border: 1px solid rgba(91, 127, 255, 0.08);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.form-item {
  margin-bottom: 20px;
  position: relative;
}

.form-label {
  font-size: 15px;
  color: #2E3A59;
  margin-bottom: 10px;
  display: flex;
  font-weight: bold;
  letter-spacing: 0.2px;
}



.form-input {
  width: 100%;
  height: 48px;
  border: 1px solid rgba(91, 127, 255, 0.2);
  border-radius: 12px;
  padding: 0 16px;
  font-size: 15px;
  color: #2E3A59;
  background-color: rgba(255, 255, 255, 0.8);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  box-shadow: 0 2px 8px rgba(31, 60, 136, 0.03);
}

.form-input:focus {
  border-color: #5B7FFF;
  background-color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.1);
}

.form-textarea {
  width: 100%;
  height: 120px;
  border: 1px solid rgba(91, 127, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 15px;
  color: #2E3A59;
  background-color: rgba(255, 255, 255, 0.8);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  box-shadow: 0 2px 8px rgba(31, 60, 136, 0.03);
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: #5B7FFF;
  background-color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.1);
}

.picker-item {
  width: 100%;
  height: 48px;
  border: 1px solid rgba(91, 127, 255, 0.2);
  border-radius: 12px;
  padding: 0 16px;
  font-size: 15px;
  color: #2E3A59;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  box-shadow: 0 2px 8px rgba(31, 60, 136, 0.03);
}

.picker-text {
  text-align: center;
  width: 100%;
  font-size: 15px;
  color: #2E3A59;
}

.plan-type-selector {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 0 -4px;
  gap: 8px;
}

.plan-type-item {
  flex: 1;
  min-width: 80px;
  height: 44px;
  margin: 4px;
  background-color: rgba(91, 127, 255, 0.05);
  border: 1px solid rgba(91, 127, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  box-shadow: 0 2px 8px rgba(31, 60, 136, 0.02);
}

.plan-type-item--active {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.15);
  transform: translateY(-1px);
}

.plan-type-item--disabled {
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}



.plan-type-item__text {
  font-size: 14px;
  color: #5B7FFF;
  font-weight: bold;
}

.plan-type-item--active .plan-type-item__text {
  color: #FFFFFF;
  font-weight: bold;
}

.priority-selector {
  display: flex;
  flex-direction: row;
  gap: 12px;
  margin-top: 5px;
}

.priority-item {
  flex: 1;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(31, 60, 136, 0.03);
}



.priority-item:

.priority-item--low {
  background-color: rgba(82, 196, 26, 0.1);
  border: 1px solid rgba(82, 196, 26, 0.2);
}

.priority-item--medium {
  background-color: rgba(250, 173, 20, 0.1);
  border: 1px solid rgba(250, 173, 20, 0.2);
}

.priority-item--high {
  background-color: rgba(255, 77, 79, 0.1);
  border: 1px solid rgba(255, 77, 79, 0.2);
}

.priority-item--active.priority-item--low {
  background: linear-gradient(135deg, #52C41A, #73D13D);
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.2);
  transform: translateY(-1px);
}

.priority-item--active.priority-item--medium {
  background: linear-gradient(135deg, #FAAD14, #FFC53D);
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(250, 173, 20, 0.2);
  transform: translateY(-1px);
}

.priority-item--active.priority-item--high {
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.2);
  transform: translateY(-1px);
}

.priority-item__text {
  font-size: 15px;
  font-weight: bold;
  position: relative;
  z-index: 1;
}

.priority-item--low .priority-item__text {
  color: #52C41A;
}

.priority-item--medium .priority-item__text {
  color: #FAAD14;
}

.priority-item--high .priority-item__text {
  color: #FF4D4F;
}

.priority-item--active .priority-item__text {
  color: #FFFFFF;
  font-weight: bold;
}

.footer {
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);

  border-top: 1px solid rgba(91, 127, 255, 0.1);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  justify-content: center;
  box-shadow: 0 -4px 16px rgba(31, 60, 136, 0.08);

}

.footer-buttons {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  max-width: 500px;
  gap: 10px;
}


  to { transform: translateY(0); }
}

.btn-cancel, .btn-primary {
  padding: 12px 0;
  border-radius: 12px;
  font-size: 15px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: transform, opacity; transition-duration: 0.25s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  flex: 1;
  height: 44px;
  position: relative;
  overflow: hidden;
  max-width: 200px;
  margin: 0 8px;
}

.btn-cancel {
  background-color: #F5F7FA;
  color: #5E6C84;
  border: 1px solid rgba(94, 108, 132, 0.1);
}

.btn-cancel:active {
  background-color: #EBEDF0;
}

.btn-primary {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.2);
}



.btn-primary:

.btn-hover {
  opacity: 0.9;
  transform: translateY(1px);
}
</style>

