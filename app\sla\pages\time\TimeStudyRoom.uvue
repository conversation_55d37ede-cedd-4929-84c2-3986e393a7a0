<template>
  <view class="study-room-container">
    <!-- ????????? -->
    <view class="header">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="nav-bar">
        <view class="nav-left" @click="goBack">
          <text class="iconfont icon-arrow-left"></text>
        </view>
        <view class="nav-title">
          <text class="title-text">{{ roomInfo.name || '???' }}</text>
        </view>
        <view class="nav-right">
          <view class="exit-icon" @click="exitRoom" v-if="!isCreator">
            <text class="iconfont icon-exit"></text>
          </view>
          <view class="exit-icon" @click="disbandRoom" v-else>
            <text class="iconfont icon-delete"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- ??????? -->
    <scroll-view class="content-container" scroll-y>
      <!-- ??????? -->
      <view class="room-info-card">
        <view class="room-header">
          <view class="room-title">
            <text class="title">{{ roomInfo.name }}</text>
            <view class="tag-container">
              <text class="tag" v-for="(tag, index) in roomInfo.tags" :key="index">{{ tag }}</text>
            </view>
          </view>
          <view class="room-members-count">
            <text class="iconfont icon-user"></text>
            <text>{{ roomInfo.currentMembers || roomInfo.currentUsers || 0 }}/{{ roomInfo.capacity || 8 }}</text>
          </view>
        </view>
        <view class="room-desc">
          <text>{{ roomInfo.description || '????' }}</text>
        </view>
      </view>

      <!-- ????? -->
      <view class="status-switcher">
        <view
          v-for="(status, index) in statusOptions"
          :key="index"
          class="status-item"
          :class="{ active: currentStatus === status.value }"
          @click="changeStatus(status.value)"
        >
          <text class="iconfont" :class="status.icon"></text>
          <text class="status-text">{{ status.label }}</text>
        </view>
      </view>

      <!-- ????? -->
      <view class="environment-container">
        <view class="section-title">
          <text>????</text>
        </view>
        <scroll-view class="environment-scroll" scroll-x show-scrollbar="false">
          <view
            v-for="(env, index) in environmentOptions"
            :key="index"
            class="environment-item"
            :class="{ active: currentEnvironment === env.value }"
            @click="changeEnvironment(env.value)"
          >
            <view class="env-icon">
              <text class="iconfont" :class="env.icon"></text>
            </view>
            <text class="env-text">{{ env.label }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- ????????? -->
      <view class="tabs-container">
        <view class="tabs-header">
          <view
            class="tab-item"
            :class="{ active: activeTab === 'members' }"
            @click="activeTab = 'members'"
          >
            <text>????</text>
          </view>
          <view
            class="tab-item"
            :class="{ active: activeTab === 'chat' }"
            @click="activeTab = 'chat'"
          >
            <text>??</text>
          </view>
        </view>
        <view class="tab-content">
          <!-- ?????? -->
          <view class="members-container" v-show="activeTab === 'members'">
            <view
              v-for="(member, index) in members"
              :key="index"
              class="member-item"
            >
              <image class="member-avatar" :src="member.avatar" mode="aspectFill"></image>
              <view class="member-info">
                <view class="member-name-row">
                  <text class="member-name">{{ member.username }}</text>
                  <text class="member-creator" v-if="member.isCreator">??</text>
                </view>
                <view class="member-status">
                  <text class="status-dot" :class="getStatusClass(member.status)"></text>
                  <text class="status-text">{{ getStatusText(member.status) }}</text>
                </view>
              </view>
              <view class="member-focus-time">
                <text>{{ formatFocusTime(member.totalFocusTime) }}</text>
              </view>
            </view>
          </view>

          <!-- ???? -->
          <view class="chat-container" v-show="activeTab === 'chat'">
            <scroll-view class="chat-messages" scroll-y :scroll-into-view="'msg-' + messages.length">
              <view
                v-for="(message, index) in messages"
                :key="index"
                class="message-item"
                :class="{
                  'system-message': message.type === 'SYSTEM_JOIN' || message.type === 'SYSTEM_LEAVE' || message.type === 'SYSTEM_DISBAND',
                  'self-message': message.senderId === userId
                }"
                :id="'msg-' + index"
              >
                <!-- ???? -->
                <view class="system-message-content" v-if="message.type === 'SYSTEM_JOIN' || message.type === 'SYSTEM_LEAVE' || message.type === 'SYSTEM_DISBAND'">
                  <text>{{ message.data.content }}</text>
                </view>

                <!-- ???? -->
                <view class="user-message-content" v-else>
                  <view class="message-sender" v-if="message.senderId !== userId">
                    <image class="sender-avatar" :src="getSenderAvatar(message.senderId)" mode="aspectFill"></image>
                    <text class="sender-name">{{ getSenderName(message.senderId) }}</text>
                  </view>
                  <view class="message-content">
                    <text>{{ message.data.content }}</text>
                  </view>
                  <text class="message-time">{{ formatMessageTime(message.timestamp) }}</text>
                </view>
              </view>
            </scroll-view>

            <view class="chat-input-container">
              <input
                class="chat-input"
                v-model="messageContent"
                placeholder="????..."
                @confirm="sendMessage"
              />
              <button class="send-btn" @click="sendMessage">??</button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- ?????? -->
    <view class="bottom-actions">
      <button class="exit-btn" v-if="!isCreator" @click="exitRoom">?????</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20, // ????????? uni.getSystemInfoSync ??
      roomId: '', // ???ID
      roomInfo: {}, // ?????
      members: [], // ????
      messages: [], // ????
      messageContent: '', // ?????
      activeTab: 'members', // ???????: members/chat
      currentStatus: 'focusing', // ????
      currentEnvironment: 'library', // ????
      isCreator: false, // ??????
      userId: 0, // ????ID
      webSocket: null, // WebSocket??
      reconnectAttempts: 0, // ??????
      maxReconnectAttempts: 5, // ????????

      // ????
      statusOptions: [
        { label: '???', value: 'focusing', icon: 'icon-focus' },
        { label: '???', value: 'question', icon: 'icon-question' },
        { label: '???', value: 'rest', icon: 'icon-rest' },
        { label: '????', value: 'away', icon: 'icon-away' }
      ],

      // ????
      environmentOptions: [
        { label: '???', value: 'library', icon: 'icon-library' },
        { label: '???', value: 'cafe', icon: 'icon-cafe' },
        { label: '??', value: 'classroom', icon: 'icon-classroom' },
        { label: '??', value: 'nature', icon: 'icon-nature' },
        { label: '???', value: 'custom', icon: 'icon-custom' }
      ]
    };
  },
  onLoad(options) {
    // ???????
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;

    // ?????ID???ID
    this.roomId = options.roomId || '';
    this.getUserInfo();

    // ???????
    this.fetchRoomDetail();
  },
  onShow() {
    // ??WebSocket??
    this.connectWebSocket();
  },
  onHide() {
    // ??WebSocket??
    this.closeWebSocket();
  },
  onUnload() {
    // ??WebSocket??
    this.closeWebSocket();
  },
  methods: {
    // ?????
    goBack() {
      uni.navigateBack();
    },

    // ??????
    getUserInfo() {
      try {
        const userInfo = uni.getStorageSync('userInfo');
        if (userInfo) {
          this.userId = userInfo.id;
        }
      } catch (e) {
        console.error('????????', e);
      }
    },

    // ???????
    fetchRoomDetail() {
      uni.showLoading({ title: '???' });
      const token = uni.getStorageSync('token');

      uni.request({
        url: '/api/time/studyroom/detail/' + this.roomId,
        method: 'GET',
        header: {
          'Authorization': 'Bearer ' + token
        },
        success: (res) => {
          if (res.data.code === 200) {
            this.roomInfo = res.data.data;
            this.members = res.data.data.members || [];
            this.isCreator = res.data.data.isSelf || false;
            this.currentEnvironment = res.data.data.environment || 'library';
          } else {
            uni.showToast({
              title: res.data.message || '??????????,
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('??????????, err);
          uni.showToast({
            title: '????????',
            icon: 'none'
          });
        },
        complete: () => {
          uni.hideLoading();
        }
      });
    },

    // ?????    changeStatus(status) {
      this.currentStatus = status;

      // ??WebSocket?????????      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {
        const message = {
          type: 'STATUS_CHANGE',
          data: {
            status: status
          },
          timestamp: new Date().getTime(),
          senderId: this.userId
        };

        this.webSocket.send(JSON.stringify(message));
      }
    },

    // ????
    changeEnvironment(environment) {
      // ??????????????
      if (!this.isCreator) {
        uni.showToast({
          title: '??????????',
          icon: 'none'
        });
        return;
      }

      this.currentEnvironment = environment;

      // ????????????      const token = uni.getStorageSync('token');

      uni.request({
        url: '/api/time/studyroom/update',
        method: 'PUT',
        header: {
          'Authorization': 'Bearer ' + token,
          'Content-Type': 'application/json'
        },
        data: {
          roomId: this.roomId,
          environment: environment
        },
        success: (res) => {
          if (res.data.code === 200) {
            uni.showToast({
              title: '??????,
              icon: 'success'
            });
          } else {
            uni.showToast({
              title: res.data.message || '??????',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('??????', err);
          uni.showToast({
            title: '????????',
            icon: 'none'
          });
        }
      });
    },

    // ?????    sendMessage() {
      if (!this.messageContent.trim()) {
        return;
      }

      // ??WebSocket?????      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {
        const message = {
          type: 'USER_MESSAGE',
          data: {
            content: this.messageContent.trim()
          },
          timestamp: new Date().getTime(),
          senderId: this.userId
        };

        this.webSocket.send(JSON.stringify(message));
        this.messageContent = '';
      } else {
        uni.showToast({
          title: '???????????',
          icon: 'none'
        });
      }
    },

    // ??WebSocket??
    connectWebSocket() {
      const token = uni.getStorageSync('token');
      // ????????WebSocket?URL??????????????localhost
      const serverHost = process.env.NODE_ENV === 'development'
        ? 'ws://127.0.0.1:8080'
        : 'wss://yourdomain.com'; // ?????????WebSocket??

      const wsUrl = `${serverHost}/api/time/ws/studyroom/${this.roomId}?token=${token}`;

      // ??????????
      this.closeWebSocket();

      // ????WebSocket??
      try {
        this.webSocket = new WebSocket(wsUrl);

        // ????
        this.webSocket.onopen = () => {
          console.log('WebSocket??????);

          // ??????
          this.webSocket.send(JSON.stringify({
            type: 'GET_HISTORY',
            timestamp: new Date().getTime(),
            senderId: this.userId
          }));
        };

        // ????
        this.webSocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            console.log('??WebSocket??:', message.type);

            switch (message.type) {
              case 'HISTORY_RESPONSE':
                // ??????
                if (message.data && message.data.messages) {
                  this.messages = message.data.messages;
                }
                break;
              case 'USER_MESSAGE':
                // ??????
                this.messages.push(message);
                // ????????                this.$nextTick(() => {
                  const messagesElement = document.getElementById('msg-' + (this.messages.length - 1));
                  if (messagesElement) {
                    messagesElement.scrollIntoView();
                  }
                });
                break;
              case 'SYSTEM_JOIN':
              case 'SYSTEM_LEAVE':
                // ??????/????
                this.messages.push(message);
                break;
              case 'SYSTEM_DISBAND':
                // ????????
                this.messages.push(message);
                // ??????????????
                uni.showModal({
                  title: '??',
                  content: '???????????,
                  showCancel: false,
                  success: () => {
                    uni.navigateBack();
                  }
                });
                break;
              case 'MEMBER_LIST_UPDATE':
                // ??????
                if (message.data && message.data.members) {
                  this.members = message.data.members;
                }
                break;
              case 'ERROR':
                // ??????
                uni.showToast({
                  title: message.data.message || '????',
                  icon: 'none'
                });
                break;
              default:
                console.log('????????', message);
            }
          } catch (e) {
            console.error('??????', e, event.data);
          }
        };

        // ????
        this.webSocket.onclose = (event) => {
          console.log('WebSocket??????, event.code, event.reason);
          if (event.code !== 1000) {
            // ??????????
            this.attemptReconnect();
          }
        };

        // ????
        this.webSocket.onerror = (error) => {
          console.error('WebSocket????', error);
          uni.showToast({
            title: '????????',
            icon: 'none'
          });
        };
      } catch (e) {
        console.error('??WebSocket????', e);
        uni.showToast({
          title: '????????',
          icon: 'none'
        });
      }
    },

    // ??????
    attemptReconnect() {
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        uni.showToast({
          title: '??????????????,
          icon: 'none',
          duration: 3000
        });
        return;
      }

      this.reconnectAttempts++;
      console.log(`?????? (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

      setTimeout(() => {
        this.connectWebSocket();
      }, 2000 * Math.min(this.reconnectAttempts, 10)); // ????????????0??    },

    // ??WebSocket??
    closeWebSocket() {
      if (this.webSocket) {
        try {
          this.webSocket.close(1000, 'Normal Closure');
        } catch (e) {
          console.error('??WebSocket????', e);
        }
        this.webSocket = null;
      }
      this.reconnectAttempts = 0;
    },

    // ?????
    exitRoom() {
      uni.showModal({
        title: '??',
        content: '??????????',
        success: (res) => {
          if (res.confirm) {
            const token = uni.getStorageSync('token');

            uni.request({
              url: '/api/time/studyroom/exit',
              method: 'POST',
              header: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
              },
              data: {
                roomId: this.roomId,
                focusTime: 0, // ??????????????
                recordFocusTime: true
              },
              success: (res) => {
                if (res.data.code === 200) {
                  uni.showToast({
                    title: '??????',
                    icon: 'success'
                  });

                  // ??WebSocket??
                  this.closeWebSocket();

                  // ????????????                  uni.$emit('refreshStudyRoomList');

                  // ??????????????                  uni.setStorageSync('needRefreshStudyRoomList', true);

                  // ??????                  setTimeout(() => {
                    uni.switchTab({
                      url: '/pages/time/index'
                    });
                  }, 1500);
                } else {
                  uni.showToast({
                    title: res.data.message || '???????',
                    icon: 'none'
                  });
                }
              },
              fail: (err) => {
                console.error('???????', err);
                uni.showToast({
                  title: '????????',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    },

    // ??????    disbandRoom() {
      uni.showModal({
        title: '??',
        content: '??????????????????',
        success: (res) => {
          if (res.confirm) {
            const token = uni.getStorageSync('token');

            uni.request({
              url: '/api/time/studyroom/disband',
              method: 'POST',
              header: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
              },
              data: {
                roomId: this.roomId
              },
              success: (res) => {
                if (res.data.code === 200) {
                    uni.showToast({
                      title: '??????',
                      icon: 'success'
                    });

                  // ??WebSocket??
                  this.closeWebSocket();

                  // ????????????                  uni.$emit('refreshStudyRoomList');

                  // ??????????????                  uni.setStorageSync('needRefreshStudyRoomList', true);

                  // ????
                  setTimeout(() => {
                    uni.switchTab({
                      url: '/pages/time/index'
                    });
                  }, 1500);
                  } else {
                    uni.showToast({
                    title: res.data.message || '????????,
                      icon: 'none'
                    });
                  }
              },
              fail: (err) => {
                console.error('????????, err);
                  uni.showToast({
                  title: '????????',
                    icon: 'none'
                  });
              }
                });
          }
        }
      });
    },

    // ????????    formatFocusTime(minutes) {
      if (!minutes) return '0??';

      if (minutes < 60) {
        return minutes + '??';
      } else {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return hours + '??' + (mins > 0 ? mins + '??' : '');
      }
    },

    // ????????    formatMessageTime(timestamp) {
      if (!timestamp) return '';

      const date = new Date(timestamp);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');

      return hours + ':' + minutes;
    },

    // ???????    getStatusClass(status) {
      switch (status) {
        case 'focusing':
          return 'status-focusing';
        case 'question':
          return 'status-question';
        case 'rest':
          return 'status-rest';
        case 'away':
          return 'status-away';
        default:
          return '';
      }
    },

    // ???????    getStatusText(status) {
      switch (status) {
        case 'focusing':
          return '????;
        case 'question':
          return '????;
        case 'rest':
          return '????;
        case 'away':
          return '????';
        default:
          return '??';
      }
    },

    // ????????    getSenderAvatar(senderId) {
      const member = this.members.find(m => m.userId === senderId);
      return member ? member.avatar : '/static/images/default-avatar.png';
    },

    // ????????    getSenderName(senderId) {
      const member = this.members.find(m => m.userId === senderId);
      return member ? member.username : '????';
    }
  }
};
</script>

<style>
.study-room-container {
  min-height: 800px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

/* ????????? */
.header {
  background-color: #5B7FFF;
  color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.status-bar {
  width: 100%;
}

.nav-bar {
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}

.nav-left {
  width: 40px;
  display: flex;
  align-items: center;
}

.nav-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: bold;
}

.nav-right {
  width: 40px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.exit-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-exit {
  font-size: 18px;
  color: #fff;
}

/* ????????*/
.content-container {
  flex: 1;
  padding: 15px;
}

.room-info-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.room-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.room-title {
  flex: 1;
  font-size: 18px;
  font-weight: bold;
}

.tag-container {
  display: inline;
}

.tag {
  background-color: #e0e0e0;
  border-radius: 4px;
  padding: 2px 8px;
  margin-left: 8px;
  font-size: 12px;
  display: flex;
}

.room-members-count {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.room-desc {
  font-size: 14px;
}

/* ????? */
.status-switcher {
  display: flex;
  margin-bottom: 15px;
}

.status-item {
  flex: 1;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.status-item.active {
  border-bottom: 2px solid #5B7FFF;
}

.status-text {
  display: flex;
  margin-top: 5px;
}

/* ??????*/
.environment-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}

.environment-scroll {
  display: flex;
  overflow-x: auto;
  padding: 5px 0;
}

.environment-item {
  flex: 0 0 auto;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.environment-item.active {
  border-bottom: 2px solid #5B7FFF;
}

.env-icon {
  font-size: 24px;
}

.env-text {
  display: flex;
  margin-top: 5px;
}

/* ??????????*/
.tabs-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.tabs-header {
  display: flex;
  margin-bottom: 15px;
}

.tab-item {
  flex: 1;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  border-bottom: 2px solid #5B7FFF;
}

.tab-content {
  flex: 1;
}

.members-container {
  margin-bottom: 15px;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 999px;
  margin-right: 10px;
}

.member-info {
  flex: 1;
}

.member-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.member-name {
  font-size: 14px;
  font-weight: bold;
}

.member-creator {
  background-color: #e0e0e0;
  border-radius: 4px;
  padding: 2px 8px;
  margin-left: 8px;
  font-size: 12px;
}

.member-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 999px;
  margin-right: 5px;
}

.status-text {
  font-size: 12px;
}

.member-focus-time {
  font-size: 12px;
}

.chat-container {
  flex: 1;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
}

.message-item {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.system-message-content {
  font-size: 14px;
}

.user-message-content {
  display: flex;
  align-items: center;
}

.message-sender {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.sender-avatar {
  width: 40px;
  height: 40px;
  border-radius: 999px;
  margin-right: 5px;
}

.sender-name {
  font-size: 14px;
}

.message-content {
  flex: 1;
}

.message-time {
  font-size: 12px;
}

.chat-input-container {
  display: flex;
  align-items: center;
  padding: 10px;
  border-top: 1px solid #e0e0e0;
}

.chat-input {
  flex: 1;
  padding: 10px;
}

.send-btn {
  padding: 10px 20px;
  background-color: #5B7FFF;
  color: #fff;
  border: none;
  border-radius: 4px;
  margin-left: 10px;
}

/* ?????? */
.bottom-actions {
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
}

.exit-btn {
  width: 100%;
  padding: 10px;
  background-color: #5B7FFF;
  color: #fff;
  border: none;
  border-radius: 4px;
}
</style>
