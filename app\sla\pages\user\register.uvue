<template>
  <view class="register-container">
    <!-- ????? -->
    <sla-navbar title="??" @back="navigateBack" />

    <sla-form class="register-form" :model="formData" :rules="rules" ref="registerForm">
      <sla-input
        v-model="formData.username as string"
        label="???"
        placeholder="??????"
        required
        clearable
        :error="$refs.registerForm?.getFieldError('username')"
      />

      <sla-input
        v-model="formData.phone as string"
        label="???"
        placeholder="??????"
        type="number"
        :maxlength="11"
        required
        clearable
        :error="$refs.registerForm?.getFieldError ? $refs.registerForm.getFieldError('phone') : ''"
      />

      <view class="verification-code">
        <sla-input
          v-model="formData.code as string"
          label="???"
          placeholder="??????"
          type="number"
          :maxlength="6"
          required
          clearable
          :error="$refs.registerForm?.getFieldError ? $refs.registerForm.getFieldError('code') : ''"
        >
          <template v-slot:suffix>
            <sla-countdown
              ref="countdown"
              @click="sendVerificationCode"
              :disabled="!isPhoneValid"
            />
          </template>
        </sla-input>
      </view>

      <sla-input
        v-model="formData.password as string"
        label="??"
        placeholder="?????"
        type="password"
        required
        :error="$refs.registerForm?.getFieldError ? $refs.registerForm.getFieldError('password') : ''"
      />

      <sla-input
        v-model="formData.confirmPassword as string"
        label="????"
        placeholder="???????"
        type="password"
        required
        :error="$refs.registerForm?.getFieldError ? $refs.registerForm.getFieldError('confirmPassword') : ''"
      />

      <view class="register-agreement">
        <checkbox :checked="agreement" @click="toggleAgreement" color="#80B8F5" />
        <text class="register-agreement__text">???????</text>
        <text class="register-agreement__link" @click="showAgreement">????</text>
        <text class="register-agreement__text">?</text>
        <text class="register-agreement__link" @click="showPrivacy">????</text>
      </view>

      <sla-button
        text="??"
        type="primary"
        size="large"
        :loading="loading"
        :disabled="!agreement"
        @click="handleRegister"
        class="register-button"
      />

      <view class="register-login">
        <text class="register-login__text">?????</text>
        <text class="register-login__link" @click="navigateToLogin">????</text>
      </view>
    </sla-form>
  </view>
</template>

<script>
import SlaButton from '../../components/user/SlaButton.uvue';
import SlaInput from '../../components/user/SlaInput.uvue';
import SlaForm from '../../components/user/SlaForm.uvue';
import SlaCountdown from '../../components/user/SlaCountdown.uvue';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import { register, sendSms } from '../../utils/api/user.js';

export default {
  components: {
    SlaButton,
    SlaInput,
    SlaForm,
    SlaCountdown,
    SlaNavbar
  },
  data() {
    return {
      // ????
      formData: {
        username: '',
        phone: '',
        code: '',
        password: '',
        confirmPassword: ''
      },
      // ??????
      rules: {
        username: [
          { required: true, message: '??????' },
          { min: 2, max: 64, message: '??????2-64???' }
        ],
        phone: [
          { required: true, message: '??????' },
          { pattern: /^1[3-9]\d{9}$/, message: '?????????' }
        ],
        code: [
          { required: true, message: '??????' },
          { pattern: /^\d{6}$/, message: '????6???' }
        ],
        password: [
          { required: true, message: '?????' },
          { min: 6, max: 20, message: '?????6-20???' },
          {
            pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,20}$/,
            message: '???????????'
          }
        ],
        confirmPassword: [
          {
            required: true,
            message: '???????',
            validator: (rule, value, callback) => {
              if (value !== this.formData.password) {
                callback('??????????');
              } else {
                callback();
              }
            }
          }
        ]
      },
      // ??????
      agreement: false,
      // ?????
      loading: false
    };
  },
  computed: {
    // ???????
    isPhoneValid() {
      return this.formData.phone && /^1[3-9]\d{9}$/.test(this.formData.phone);
    }
  },
  methods: {
    // ?????
    sendVerificationCode() {
      // ????????????
      if (!this.isPhoneValid) {
        uni.showToast({
          title: '?????????',
          icon: 'none'
        });
        return false;
      }

      // ?????
      uni.showLoading({
        title: '???...'
      });

      // ?????
      sendSms(this.formData.phone, 0).then(() => {
        uni.hideLoading();
        uni.showToast({
          title: '???????',
          icon: 'success'
        });
        // ?????
        const countdown = this.$refs.countdown as any;
        if (countdown && typeof countdown.start === 'function') {
          countdown.start();
        }
      }).catch(err => {
        uni.hideLoading();
        uni.showToast({
          title: err.message || '???????',
          icon: 'none'
        });
      });
    },

    // ????
    handleRegister() {
      if (!this.agreement) {
        uni.showToast({
          title: '?????????????',
          icon: 'none'
        });
        return;
      }

      // ????
      const registerForm = this.$refs.registerForm as any;
      if (registerForm && typeof registerForm.validate === 'function') {
        registerForm.validate().then(({ valid, errors }) => {
          if (!valid) {
            const firstError = Object.values(errors)[0] as string;
            uni.showToast({
              title: firstError,
              icon: 'none'
            });
            return;
          }

        this.loading = true;

        // ??????
        const registerParams = {
          username: this.formData.username,
          phone: this.formData.phone,
          code: this.formData.code,
          password: this.formData.password
        };

        // ??????
        register(registerParams).then(res => {
          uni.showToast({
            title: '????',
            icon: 'success'
          });

          // ??token?????
          if (res.data) {
            uni.setStorageSync('accessToken', res.data.accessToken);
            uni.setStorageSync('refreshToken', res.data.refreshToken);
            uni.setStorageSync('userInfo', res.data);
          }

          // ?????
          setTimeout(() => {
            uni.switchTab({
              url: '/pages/index/index'
            });
          }, 1500);
        }).catch(err => {
          uni.showToast({
            title: err.message || '????',
            icon: 'none'
          });
        }).finally(() => {
          this.loading = false;
        });
      });
      } else {
        uni.showToast({
          title: '??????',
          icon: 'none'
        });
      }
    },

    // ????????
    toggleAgreement() {
      this.agreement = !this.agreement;
    },

    // ??????
    showAgreement() {
      uni.showModal({
        title: '????',
        content: '????????...',
        showCancel: false
      });
    },

    // ??????
    showPrivacy() {
      uni.showModal({
        title: '????',
        content: '????????...',
        showCancel: false
      });
    },

    // ?????
    navigateBack() {
      // ??????????????splash??
      uni.navigateTo({
        url: '/pages/user/login'
      });
    },

    // ???????
    navigateToLogin() {
      uni.navigateTo({
        url: '/pages/user/login'
      });
    }
  }
}
</script>

<style>
.register-container {
  min-height: 800px;
  background: linear-gradient(135deg, #4361ee 0%, #3a86ff 100%); /* ???????????*/
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* ?????? */




.register-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  margin-bottom: 30px;
  padding: 0 10px;
}

.register-back {
  padding: 8px;
}

.register-back__icon {
  font-size: 24px;
  color: #333333;
}



.register-form {
  background-color: rgba(255, 255, 255, 0.85); /* ??????? */
   /* ?????????????*/
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.6); /* ????????*/
}

.verification-code {
  position: relative;
}

.register-agreement {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  margin: 16px 0;
}

.register-agreement__text {
  font-size: 14px;
  color: #666666;
  margin-left: 4px;
}

.register-agreement__link {
  font-size: 14px;
  color: #8A2BE2; /* ??????*/
}

.register-button {
  width: 100%;
  margin-top: 24px;
  background: linear-gradient(135deg, #8A2BE2, #9370DB); /* ???? */
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(138, 43, 226, 0.3);
}

.register-login {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 24px;
  position: relative;
  z-index: 1;
}

.register-login__text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.register-login__link {
  font-size: 14px;
  color: #FFFFFF;
  margin-left: 4px;
  font-weight: bold;
  text-decoration: underline;
}


</style>

