<template>
  <view class="records-container">
    <!-- 顶部导航栏 -->
    <sla-navbar title="专注记录" :show-back="true">
      <template #left>
        <view class="back-button" @click="handleBack">
          <text class="back-button-icon">←</text>
        </view>
      </template>
      <template #right>
        <view class="action-button" @click="clearAllRecords">
          <text class="action-button__text">清空</text>
        </view>
      </template>
    </sla-navbar>

    <!-- 统计卡片 -->
    <view class="stats-card">
      <view class="stats-item">
        <text class="stats-value">{{ totalDuration }}</text>
        <text class="stats-label">总专注时长(分钟)</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{ totalSessions }}</text>
        <text class="stats-label">专注次数</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{ averageDuration }}</text>
        <text class="stats-label">平均时长(分钟)</text>
      </view>
    </view>

    <!-- 筛选区域 -->
    <view class="filters-container">
      <!-- 时间筛选 -->
      <view class="filter-section">
        <view class="filter-section-header">
          <text class="filter-section-title">时间段</text>
          <view class="filter-section-icon">
            <text class="filter-icon">⏱</text>
          </view>
        </view>
        <!-- 时间筛选项 -->
        <view class="filter-bar">
          <view class="filter-item" :class="{'filter-item--active': timeFilter === 'all'}" @click="setTimeFilter('all')">
            <text class="filter-text">全部</text>
          </view>
          <view class="filter-item" :class="{'filter-item--active': timeFilter === 'today'}" @click="setTimeFilter('today')">
            <text class="filter-text">今天</text>
          </view>
          <view class="filter-item" :class="{'filter-item--active': timeFilter === 'week'}" @click="setTimeFilter('week')">
            <text class="filter-text">本周</text>
          </view>
          <view class="filter-item" :class="{'filter-item--active': timeFilter === 'month'}" @click="setTimeFilter('month')">
            <text class="filter-text">本月</text>
          </view>
        </view>
      </view>

      <!-- 类型筛选区域 -->
      <view class="filter-section">
        <view class="filter-section-header">
          <text class="filter-section-title">类型</text>
          <view class="filter-section-icon type-icon">
            <text class="filter-icon">📊</text>
          </view>
        </view>
        <!-- 类型筛选项 -->
        <view class="type-filter-bar">
          <view class="type-filter-item" :class="{'type-filter-item--active': typeFilter === 'all'}" @click="setTypeFilter('all')">
            <text class="type-filter-text">全部</text>
          </view>
          <view class="type-filter-item" :class="{'type-filter-item--active': typeFilter === 'personal'}" @click="setTypeFilter('personal')">
            <text class="type-filter-text">个人</text>
          </view>
          <view class="type-filter-item" :class="{'type-filter-item--active': typeFilter === 'studyroom'}" @click="setTypeFilter('studyroom')">
            <text class="type-filter-text">自习室</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 记录列表 -->
    <scroll-view class="records-list" scroll-y>
      <view v-if="focusRecords.length === 0" class="empty-state">
        <text class="empty-state__text">暂无专注记录</text>
      </view>
      <view v-else>
        <!-- 按日期分组 -->
        <view v-for="(group, date) in groupedRecords" :key="date" class="record-group">
          <view class="record-date">
            <text class="record-date__text">{{ formatDateHeader(date) }}</text>
            <view class="record-date__line"></view>
          </view>

          <view v-for="record in group" :key="record.id" class="record-item">
            <view class="record-time">
              <text class="record-time__text">{{ formatRecordTime(record.date) }}</text>
              <text class="record-duration">{{ record.duration }}??</text>
            </view>
            <view class="record-card">
              <view class="record-content">
                <view class="record-info">
                  <view class="record-task-container">
                    <text class="record-task">{{ record.task }}</text>
                    <view v-if="record.recordType === 1" class="record-type-badge study-room-badge">
                      <text class="record-type-text">自习室</text>
                    </view>
                    <view v-else-if="record.recordType === 0" class="record-type-badge personal-badge">
                      <text class="record-type-text">个人</text>
                    </view>
                  </view>
                  <text class="record-mode">{{ getModeText(record.mode) }}</text>
                </view>
                <view class="record-actions">
                  <view class="record-delete" @click.stop="deleteRecord(record.recordId)">
                    <text class="record-delete__icon">🗑️</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts">
import { ref, computed, reactive } from 'vue';
import { IRefs, FocusRecord } from '../../utils/types';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';

export default {
  components: {
    SlaNavbar
  },
  data() {
    return {
      focusRecords: [] as FocusRecord[],
      timeFilter: 'all' as string, // 'all', 'today', 'week', 'month'
      typeFilter: 'all' as string, // 'all', 'personal', 'studyroom'
      dateRange: {
        start: null,
        end: null
      },
      // ????
      totalSessions: 0,
      totalDuration: 0,
      averageDuration: 0,
      // ????
      loading: false
    }
  },
  computed: {
    // ????????????
    filteredRecords(): FocusRecord[] {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - today.getDay());
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

      return this.focusRecords.filter(record => {
        // ????
        const recordDate = this.parseDate(record.date);
        if (!recordDate) return false;

        let passTimeFilter = true;
        switch (this.timeFilter) {
          case 'today':
            passTimeFilter = recordDate >= today;
            break;
          case 'week':
            passTimeFilter = recordDate >= weekStart;
            break;
          case 'month':
            passTimeFilter = recordDate >= monthStart;
            break;
          default:
            passTimeFilter = true;
        }

        if (!passTimeFilter) return false;

        // ????
        switch (this.typeFilter) {
          case 'personal':
            return record.recordType === 0;
          case 'studyroom':
            return record.recordType === 1;
          default:
            return true;
        }
      });
    },

    // ????????
    groupedRecords(): Record<string, FocusRecord[]> {
      const groups: Record<string, FocusRecord[]> = {};

      this.filteredRecords.forEach(record => {
        const dateStr = this.getDateOnly(record.date);
        if (!groups[dateStr]) {
          groups[dateStr] = [];
        }
        groups[dateStr].push(record);
      });

      // ?????????
      const sortedGroups: Record<string, FocusRecord[]> = {};
      Object.keys(groups)
        .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
        .forEach(key => {
          sortedGroups[key] = groups[key];
        });

      return sortedGroups;
    },

    // 总专注时长（分钟）
    totalFocusTime() {
      return this.filteredRecords.reduce((sum, record) => sum + record.duration, 0);
    },

    // 平均专注时长
    averageFocusTime() {
      if (this.filteredRecords.length === 0) return 0;
      return Math.round(this.totalFocusTime / this.filteredRecords.length);
    }
  },
  onLoad() {
    this.loadFocusRecords();
    this.loadFocusStats();
  },
  onShow() {
    // 每次显示页面时重新加载数据
    this.loadFocusRecords();
    this.loadFocusStats();
  },
  methods: {
    // 处理返回按钮
    handleBack() {
      try {
        // 使用uni-app的API进行返回
        uni.navigateBack({
          delta: 1
        });
      } catch (e) {
        console.error('返回失败', e);
        // 如果返回失败，使用switchTab跳转到首页
        uni.switchTab({
          url: '/pages/time/index'
        });
      }
    },

    // 加载专注记录
    loadFocusRecords(customParams = null) {
      // 显示加载中
      uni.showLoading({
        title: '加载中...'
      });

      // 导入API
      import('../../utils/api/time.js').then(api => {
        const { getFocusRecords } = api;

        // 准备参数
        const params = customParams || {
          pageNum: 1,
          pageSize: 100 // 一次获取足够多的记录
        };

        // 如果没有自定义参数，根据当前筛选条件设置参数
        if (!customParams) {
          // 根据时间筛选设置timeRange参数
          if (this.timeFilter !== 'all') {
            params.timeRange = this.timeFilter;
          }

          // 根据类型筛选设置recordType参数
          if (this.typeFilter !== 'all') {
            params.recordType = this.typeFilter === 'personal' ? 0 : 1;
          }

          // 如果有日期范围，设置日期参数
          if (this.dateRange.start && this.dateRange.end) {
            params.startDate = this.formatDate(this.dateRange.start);
            params.endDate = this.formatDate(this.dateRange.end);
          }
        }

        console.log('加载专注记录, 参数:', params);

        // 调用API获取数据
        getFocusRecords(params)
          .then(res => {
            if (res.code === 200 && res.data) {
              // 更新记录
              this.focusRecords = res.data.list || [];

              // 如果没有记录，显示提示
              if (this.focusRecords.length === 0) {
                uni.showToast({
                  title: '没有记录',
                  icon: 'none'
                });
              }
            } else {
              this.focusRecords = [];
              uni.showToast({
                title: '获取失败',
                icon: 'none'
              });
            }
          })
          .catch(err => {
            console.error('获取专注记录失败:', err);
            this.focusRecords = [];
            uni.showToast({
              title: '获取失败',
              icon: 'none'
            });
          })
          .finally(() => {
            uni.hideLoading();
          });
      }).catch(err => {
        console.error('导入API失败:', err);
        uni.hideLoading();
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      });
    },

    // 加载专注统计数据
    loadFocusStats() {
      // 导入API
      import('../../utils/api/time.js').then(api => {
        const { getFocusStats } = api;

        // 准备参数
        const timeRange = this.timeFilter === 'all' ? 'year' : this.timeFilter;

        console.log('加载专注统计, timeRange:', timeRange);

        // 调用API获取统计数据
        getFocusStats(timeRange)
          .then(res => {
            console.log('获取专注统计数据结果:', res);
            if (res.code === 200 && res.data) {
              // 更新统计数据
              this.totalSessions = res.data.totalSessions || 0;
              this.totalDuration = res.data.totalDuration || 0;
              this.averageDuration = res.data.averageDuration || 0;
            }
          })
          .catch(err => {
            console.error('获取专注统计失败:', err);
          });
      }).catch(err => {
        console.error('导入API失败:', err);
      });
    },

    // 格式化日期为yyyy-MM-dd格式
    formatDate(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 解析日期
    parseDate(dateStr: string): Date | null {
      if (!dateStr) return null;

      // 处理"今天 HH:MM"格式
      if (dateStr.startsWith('今天')) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const timeStr = dateStr.replace('今天', '').trim();
        const [hours, minutes] = timeStr.split(':').map(Number);
        today.setHours(hours, minutes, 0, 0);
        return today;
      }

      // 处理"昨天 HH:MM"格式
      if (dateStr.startsWith('昨天')) {
        const now = new Date();
        const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
        const timeStr = dateStr.replace('昨天', '').trim();
        const [hours, minutes] = timeStr.split(':').map(Number);
        yesterday.setHours(hours, minutes, 0, 0);
        return yesterday;
      }

      // 处理其他标准日期格式
      return new Date(dateStr);
    },

    // 获取日期部分
    getDateOnly(dateStr: string): string {
      const date = this.parseDate(dateStr);
      if (!date) return dateStr;

      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // 设置时间筛选
    setTimeFilter(filter: string) {
      if (this.timeFilter === filter) return;

      this.timeFilter = filter;
      // 重新加载数据
      this.loadFocusRecords();
      this.loadFocusStats();
    },

    // 设置类型筛选
    setTypeFilter(filter: string) {
      if (this.typeFilter === filter) return;

      this.typeFilter = filter;

      // 构建API请求参数，根据筛选条件
      const params: any = {
        pageNum: 1,
        pageSize: 100
      };

      // 根据时间筛选设置timeRange参数
      if (this.timeFilter !== 'all') {
        params.timeRange = this.timeFilter;
      }

      // 根据类型筛选设置recordType参数
      if (filter !== 'all') {
        params.recordType = filter === 'personal' ? 0 : 1;
      }

      // 加载数据
      this.loadFocusRecords(params);
    },

    // 格式化记录时间显示
    formatRecordTime(dateStr: string): string {
      if (dateStr.includes(':')) {
        return dateStr.split(' ')[1];
      }
      return dateStr;
    },

    // 格式化日期标题
    formatDateHeader(dateStr: string): string {
      const date = new Date(dateStr);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      if (date.getFullYear() === today.getFullYear() &&
          date.getMonth() === today.getMonth() &&
          date.getDate() === today.getDate()) {
        return '今天';
      }

      if (date.getFullYear() === yesterday.getFullYear() &&
          date.getMonth() === yesterday.getMonth() &&
          date.getDate() === yesterday.getDate()) {
        return '昨天';
      }

      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    },

    // 获取模式文本
    getModeText(mode: string): string {
      switch(mode) {
        case 'pomodoro': return '番茄钟';
        case 'countdown': return '倒计时';
        case 'stopwatch': return '正计时';
        default: return '未知模式';
      }
    },

    // 删除记录
    deleteRecord(recordId: number) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条专注记录吗？',
        success: (res) => {
          if (res.confirm) {
            // 导入API
            import('../../utils/api/time.js').then(api => {
              const { deleteFocusRecord } = api;

              // 显示加载中
              uni.showLoading({
                title: '删除中...'
              });

              console.log('删除记录ID:', recordId);

              // 调用API删除记录
              deleteFocusRecord(recordId)
                .then(res => {
                  uni.hideLoading();
                  if (res.code === 200) {
                    // 从本地列表中删除
                    const index = this.focusRecords.findIndex(record => record.recordId === recordId);
                    if (index !== -1) {
                      this.focusRecords.splice(index, 1);
                    }

                    // 重新加载统计数据
                    this.loadFocusStats();

                    uni.showToast({
                      title: '删除成功',
                      icon: 'success'
                    });
                  } else {
                    console.error('删除记录失败:', res.message || '未知错误');
                    uni.showToast({
                      title: '删除失败',
                      icon: 'none'
                    });
                  }
                })
                .catch(err => {
                  uni.hideLoading();
                  console.error('删除记录失败:', err);
                  uni.showToast({
                    title: '删除失败',
                    icon: 'none'
                  });
                });
            }).catch(err => {
              uni.hideLoading();
              console.error('导入API失败:', err);
              uni.showToast({
                title: '加载API失败',
                icon: 'none'
              });
            });
          }
        }
      });
    },

    // 清空所有记录
    clearAllRecords() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有专注记录吗？此操作不可恢复',
        success: (res) => {
          if (res.confirm) {
            // 注意：这里使用了批量删除的方式
            // 实际项目中应该调用批量删除接口
            // 而不是循环调用单个删除接口

            // 显示加载中
            uni.showLoading({
              title: '清空中...'
            });

            // ??API
            import('../../utils/api/time.js').then(api => {
              const { deleteFocusRecord } = api;

              // ?????????Promise??
              const deletePromises = this.focusRecords.map(record =>
                deleteFocusRecord(record.recordId)
              );

              // ???????????              Promise.all(deletePromises)
                .then(() => {
                  // ????????
                  this.focusRecords = [];

                  // ????????
                  this.loadFocusStats();

                  uni.showToast({
                    title: '清空成功',
                    icon: 'success'
                  });
                })
                .catch(err => {
                  console.error('??????:', err);
                  uni.showToast({
                    title: '??????',
                    icon: 'none'
                  });
                })
                .finally(() => {
                  uni.hideLoading();
                  // ????????????????                  this.loadFocusRecords();
                });
            }).catch(err => {
              console.error('??API??:', err);
              uni.hideLoading();
              uni.showToast({
                title: '??API??',
                icon: 'none'
              });
            });
          }
        }
      });
    }
  }
}
</script>

<style>
/* ???? */
.records-container {
  min-height: 800px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom, #eef2ff, #f5f7fa);
}

/* ?????? */
.back-button {
  padding: 8px;
  margin-left: -8px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.back-button:active {
  transform: scale(0.92);
}

.back-button-icon {
  font-size: 24px;
  color: #5B7FFF;
  font-weight: normal;
}

/* ?????? */
.action-button {
  padding: 6px 12px;
  border-radius: 12px;
  background-color: rgba(255, 77, 79, 0.1);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.action-button:active {
  transform: scale(0.92);
}

.action-button__text {
  font-size: 14px;
  color: #FF4D4F;
  font-weight: bold;
}

/* ???? */
.stats-card {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 14px;
  padding: 16px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  box-shadow: 0 6px 16px rgba(31, 60, 136, 0.06);
  position: relative;
  overflow: hidden;
}



.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.stats-item:hover {
  transform: translateY(-2px);
}

.stats-value {
  font-size: 24px;
  font-weight: 700;
  color: #5B7FFF;
  margin-bottom: 6px;
}

.stats-label {
  font-size: 13px;
  color: #5E6C84;
  font-weight: bold;
}

/* ??????*/
.filters-container {
  margin: 0 16px 16px;
}

/* ??????*/
.filter-section {
  margin-bottom: 12px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(31, 60, 136, 0.05);
  padding: 10px;

}

/* ??????*/
.filter-section-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 0 6px;
}

.filter-section-title {
  font-size: 14px;
  font-weight: bold;
  color: #2E3A59;
}

.filter-section-icon {
  width: 24px;
  height: 24px;
  border-radius: 999px;
  background-color: rgba(91, 127, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.type-icon {
  background-color: rgba(76, 175, 80, 0.1);
}

.filter-icon {
  font-size: 14px;
}

/* ??????*/
.filter-bar {
  display: flex;
  flex-direction: row;
  background-color: #F5F7FA;
  border-radius: 10px;
  overflow: hidden;
  padding: 3px;
}

.filter-item {
  flex: 1;
  padding: 8px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.filter-item--active {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.15);
}

.filter-text {
  font-size: 13px;
  font-weight: bold;
  color: #5E6C84;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.filter-item--active .filter-text {
  color: #FFFFFF;
  font-weight: bold;
}

/* ??????*/
.type-filter-bar {
  display: flex;
  flex-direction: row;
  background-color: #F5F7FA;
  border-radius: 10px;
  overflow: hidden;
  padding: 3px;
}

.type-filter-item {
  flex: 1;
  padding: 8px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.type-filter-item--active {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.15);
}

.type-filter-text {
  font-size: 13px;
  font-weight: bold;
  color: #5E6C84;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.type-filter-item--active .type-filter-text {
  color: #FFFFFF;
  font-weight: bold;
}

/* ???? */
.records-list {
  flex: 1;
  padding: 0 16px;
  margin-bottom: 16px;
}

/* ????*/
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-state__text {
  font-size: 16px;
  color: #5E6C84;
  font-weight: bold;
}

.record-group {
  margin-bottom: 16px;
}

.record-date {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
}

.record-date__text {
  font-size: 14px;
  font-weight: bold;
  color: #2E3A59;
  margin-right: 10px;
  flex-shrink: 0;
  letter-spacing: 0.2px;
}

.record-date__line {
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, rgba(91, 127, 255, 0.3), rgba(128, 184, 245, 0.1));
}

.record-item {
  margin-bottom: 16px;
}

.record-time {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 6px;
  padding: 0 8px;
}

.record-time__text {
  font-size: 13px;
  color: #5E6C84;
  font-weight: bold;
}

.record-duration {
  font-size: 13px;
  color: #5B7FFF;
  font-weight: bold;
  background-color: rgba(91, 127, 255, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
}

.record-card {
  background-color: #FFFFFF;
  border-radius: 14px;
  padding: 16px;
  box-shadow: 0 6px 16px rgba(31, 60, 136, 0.06);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  position: relative;
  overflow: hidden;
}



.record-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(31, 60, 136, 0.08);
}

.record-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.record-info {
  flex: 1;
  padding-left: 8px;
}

.record-task-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 6px;
}

.record-task {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  display: flex;
  letter-spacing: 0.2px;
  margin-right: 8px;
}

.record-type-badge {
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 2px;
}

.study-room-badge {
  background-color: #e8f0ff;
  border: 1px solid #c0d0ff;
}

.personal-badge {
  background-color: #f0f8e8;
  border: 1px solid #d0e8c0;
}

.record-type-text {
  font-size: 8px;
  font-weight: bold;
}

.study-room-badge .record-type-text {
  color: #4361D8;
}

.personal-badge .record-type-text {
  color: #4CAF50;
}

.record-mode {
  font-size: 13px;
  color: #5E6C84;
  display: flex;
  font-weight: bold;
}

.record-actions {
  margin-left: 12px;
}

.record-delete {
  width: 32px;
  height: 32px;
  border-radius: 999px;
  background-color: rgba(255, 77, 79, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.record-delete:active {
  transform: scale(0.92);
  background-color: rgba(255, 77, 79, 0.2);
}

.record-delete__icon {
  font-size: 18px;
  color: #FF4D4F;
}

/* ????*/
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 0;
}

.empty-text {
  font-size: 16px;
  color: #5E6C84;
  text-align: center;
  font-weight: bold;
}
</style>
