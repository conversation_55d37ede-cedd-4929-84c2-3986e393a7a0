<template>
  <view class="profile-container profile-page">
    <!-- ????? -->
    <view class="profile-not-login" v-if="!isLoggedIn">
      <image class="profile-not-login__image" src="/static/images/not-login.png" mode="aspectFit"></image>
      <text class="profile-not-login__text">??????????</text>
      <sla-button
        text="????"
        type="primary"
        size="medium"
        @click="navigateToLogin"
        class="profile-not-login__button"
      />
    </view>

    <!-- ????? -->
    <view class="profile-content" v-else>
      <!-- ??????????? -->
      <view class="profile-header">
        <view class="profile-header-bg"></view>

        <!-- ??????? -->
        <sla-navbar title="????" :show-back="false"></sla-navbar>

        <view class="profile-header-info">
          <view class="profile-avatar-container">
            <sla-avatar
              :src="userInfo.avatar"
              size="large"
              editable
              @change="handleAvatarChange"
              class="profile-avatar"
            />
          </view>
          <view class="profile-info">
            <text class="profile-info__name">{{ truncateName(userInfo.username) }}</text>
            <text class="profile-info__identity">{{ getIdentityText(userInfo.identity) }}</text>
          </view>
          <view class="profile-edit-btn" @click="navigateToEditProfile">
            <text class="profile-edit-btn__text">??</text>
          </view>
        </view>
      </view>

      <!-- ?????? -->
      <view class="profile-info-card profile-username-card">
        <view class="profile-card-header">
          <text class="profile-card-title">?? ????</text>
        </view>
        <view class="profile-card-body">
          <view class="profile-info-item">
            <text class="profile-info-item__label">???</text>
            <text class="profile-info-item__value">{{ truncateName(userInfo.username) }}</text>
          </view>

          <view class="profile-info-divider"></view>

          <view class="profile-info-item">
            <text class="profile-info-item__label">??</text>
            <text class="profile-info-item__value">{{ getIdentityText(userInfo.identity) }}</text>
          </view>
        </view>
      </view>

      <!-- ?????? -->
      <view class="profile-motto-card">
        <view class="profile-motto-header">
          <view class="profile-motto-title-container">
            <text class="profile-motto-header__title">? ????</text>
          </view>
        </view>
        <view class="profile-motto-content">
          <text class="profile-motto-text">{{ userInfo.signature || '???????????????' }}</text>
        </view>
      </view>

      <!-- ?????? -->
      <view class="profile-function-card">
        <view class="profile-card-header">
          <text class="profile-card-title">?? ????</text>
        </view>
        <view class="profile-card-body">
          <view class="profile-card profile-card--password" @click="navigateToChangePassword">
            <view class="profile-card__icon">??</view>
            <view class="profile-card__content">
              <text class="profile-card__title">????</text>
            </view>
            <text class="profile-card__arrow"></text>
          </view>

          <view class="profile-card profile-card--feedback" @click="showFeedback">
            <view class="profile-card__icon">??</view>
            <view class="profile-card__content">
              <text class="profile-card__title">????</text>
            </view>
            <text class="profile-card__arrow"></text>
          </view>

          <view class="profile-card profile-card--about" @click="showAbout">
            <view class="profile-card__icon">??</view>
            <view class="profile-card__content">
              <text class="profile-card__title">????</text>
            </view>
            <text class="profile-card__arrow"></text>
          </view>
        </view>
      </view>

      <!-- ?????? -->
      <view class="profile-logout" @click="handleLogout">
        <text class="profile-logout__text">????</text>
      </view>
    </view>

    <!-- ???????? -->
    <custom-tab-bar
      :current="4"
      :showAiToolbox="false"
      @centerClick="navigateToAI"
      @tabClick="handleTabClick"
    ></custom-tab-bar>
  </view>
</template>

<script>
import SlaButton from '../../components/user/SlaButton.uvue';
import SlaAvatar from '../../components/user/SlaAvatar.uvue';
import SlaCard from '../../components/user/SlaCard.uvue';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import CustomTabBar from '../../components/common/CustomTabBar.uvue';
import { getUserInfo, logout, uploadFile } from '../../utils/api/user.js';

export default {
  components: {
    SlaButton,
    SlaAvatar,
    SlaCard,
    SlaNavbar,
    CustomTabBar
  },
  data() {
    return {
      // ?????
      isLoggedIn: false,
      // ????
      userInfo: {
        userId: 0,
        username: '',
        phone: '',
        avatar: '',
        gender: 0,
        identity: 0,
        signature: '',
        birthday: '',
        status: 0,
        createTime: ''
      }
    };
  },
  onShow() {
    // ??????
    this.checkLoginStatus();

    // ????????????
    if (this.isLoggedIn) {
      this.fetchUserInfo();
    }
  },
  methods: {
    // ??????
    checkLoginStatus() {
      const token = uni.getStorageSync('accessToken');
      this.isLoggedIn = !!token;

      // ?????????????????
      if (this.isLoggedIn) {
        const cachedUserInfo = uni.getStorageSync('userInfo');
        if (cachedUserInfo) {
          this.userInfo = cachedUserInfo;
        }
      }
    },

    // ??????
    fetchUserInfo() {
      getUserInfo().then(res => {
        if (res.data) {
          this.userInfo = res.data;
          // ??????
          uni.setStorageSync('userInfo', res.data);
        }
      }).catch(err => {
        console.error('????????', err);
      });
    },

    // ??????
    handleAvatarChange(filePath) {
      uni.showLoading({
        title: '????..'
      });

      // ????
      uploadFile(filePath, 0).then(res => {
        if (res.data) {
          // ????
          this.userInfo.avatar = res.data;
          // ????
          uni.setStorageSync('userInfo', this.userInfo);

          uni.showToast({
            title: '??????',
            icon: 'success'
          });
        }
      }).catch(err => {
        uni.showToast({
          title: '??????',
          icon: 'none'
        });
      }).finally(() => {
        uni.hideLoading();
      });
    },

    // 处理退出登录
    handleLogout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            logout().then(() => {
              uni.showToast({
                title: '退出成功',
                icon: 'success'
              });

              // ???????              this.isLoggedIn = false;
              this.userInfo = {
                userId: 0,
                username: '',
                phone: '',
                avatar: '',
                gender: 0,
                identity: 0,
                signature: '',
                birthday: '',
                status: 0,
                createTime: ''
              };

              // ??????
              setTimeout(() => {
                uni.navigateTo({
                  url: '/pages/user/login'
                });
              }, 1500);
            }).catch(err => {
              uni.showToast({
                title: err.message || '退出失败',
                icon: 'none'
              });
            });
          }
        }
      });
    },

    // ??????
    formatPhone(phone) {
      if (!phone) return '';
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },

    // ????
    truncateName(name) {
      if (!name) return '';
      return name.length > 10 ? name.substring(0, 10) + '...' : name;
    },

    // 获取身份文本
    getIdentityText(identity) {
      const identityOptions = ['学生', '教师', '职员', '其他', '家长', '访客'];
      return identityOptions[identity] || '未知';
    },

    // ??????
    navigateToLogin() {
      uni.navigateTo({
        url: '/pages/user/login'
      });
    },

    // ????????
    navigateToEditProfile() {
      uni.navigateTo({
        url: '/pages/user/edit-profile'
      });
    },

    // ????????
    navigateToChangePassword() {
      uni.navigateTo({
        url: '/pages/user/change-password'
      });
    },

    // ??????
    showFeedback() {
      uni.showModal({
        title: '意见反馈',
        content: '感谢您的反馈，我们会持续改进',
        showCancel: false
      });
    },

    // 关于我们
    showAbout() {
      uni.showModal({
        title: '关于我们',
        content: '学习助手 v1.0.0\n专注于提升学习效率',
        showCancel: false
      });
    },

    // ???AI??
    navigateToAI(showToolbox = false) {
      uni.switchTab({
        url: '/pages/ai/index'
      });

      // ??????????AI??????????      uni.setStorageSync('showAiToolbox', showToolbox);
    },

    // ??????????    handleTabClick(index) {
      // ??????????CustomTabBar??????????????      console.log('Tab clicked:', index);
    },

    // ????
    uploadAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];

          // ????????          uni.showLoading({
            title: '????..',
            mask: true
          });

          // ??????
          uploadFile(tempFilePath, 0)
            .then(res => {
              if (res.code === 200) {
                // ?????????URL
                const avatarUrl = res.data;

                // ????????????                const userInfo = uni.getStorageSync('userInfo') || {};
                userInfo.avatar = avatarUrl;

                // ?????????id??????????
                if (!userInfo.userId || userInfo.userId === null) {
                  // ?????????????hashCode????ID
                  if (userInfo.username) {
                    // ??????hashCode??
                    const hashCode = str => {
                      let hash = 0;
                      for (let i = 0; i < str.length; i++) {
                        const char = str.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // Convert to 32bit integer
                      }
                      return Math.abs(hash); // ??????                    };

                    userInfo.userId = hashCode(userInfo.username);
                    console.log('userId?????????hashCode????ID:', userInfo.userId);
                  } else {
                    // ???????????????????ID
                    userInfo.userId = new Date().getTime();
                    console.log('userId?????????????????ID:', userInfo.userId);
                  }
                }

                // ??id??????????                if (userInfo.id === undefined || userInfo.id === null) {
                  userInfo.id = userInfo.userId;
                  console.log('??id??????????', userInfo.id);
                }

                uni.setStorageSync('userInfo', userInfo);

                // ????????
                this.userInfo.avatar = avatarUrl;

                // ??????????????                uni.hideLoading();
                uni.showToast({
                  title: '??????',
                  icon: 'success'
                });
              } else {
                // ????
                uni.hideLoading();
                uni.showToast({
                  title: res.message || '????',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              // ????
              uni.hideLoading();
              uni.showToast({
                title: '????????',
                icon: 'none'
              });
              console.error('??????:', err);
            });
        }
      });
    }
  }
}
</script>

<style>
.profile-container {
  min-height: 800px;
  background-color: #f7f9fc;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-bottom: 68px; /* ?????????? */
  background-image:
    radial-gradient(circle at 90% 10%, rgba(138, 123, 255, 0.1) 0%, rgba(138, 123, 255, 0) 40%),
    radial-gradient(circle at 10% 90%, rgba(91, 127, 255, 0.08) 0%, rgba(91, 127, 255, 0) 50%),
    linear-gradient(to bottom, #f7faff, #f5f8fc);
  background-attachment: fixed;
}

/* ????????*/
.profile-not-login {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 32px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(250, 250, 255, 0.8));
  margin: 40px 24px;
  border-radius: 24px;
  box-shadow: 0 16px 32px rgba(91, 127, 255, 0.1),
              0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.7);

  position: relative;
  overflow: hidden;
}





.profile-not-login__image {
  width: 180px;
  height: 180px;
  margin-bottom: 30px;
  filter: drop-shadow(0 10px 15px rgba(91, 127, 255, 0.2));
  z-index: 1;

}


  50% { transform: translateY(-10px); }
}

.profile-not-login__text {
  font-size: 17px;
  color: #4A6FE3;
  margin-bottom: 30px;
  text-align: center;
  font-weight: bold;
  letter-spacing: 0.2px;
  z-index: 1;
}

.profile-not-login__button {
  width: 220px;
  z-index: 1;
  filter: drop-shadow(0 6px 12px rgba(91, 127, 255, 0.15));
}

/* ??????????*/
.profile-header {
  position: relative;
  padding-bottom: 24px;
  margin-bottom: 16px;
  z-index: 2;
  overflow: hidden;
}

.profile-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, #4361ee, #3a86ff, #4361ee); /* ?????? */
  background-size: 300% 300%;

  z-index: 1;
  box-shadow: 0 8px 24px rgba(67, 97, 238, 0.15);
}

/* ??????????*/

  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* ???????? */


/* ?????? */
.profile-header-info {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 20px 16px 0;
  background: rgba(255, 255, 255, 0.15);

  border-radius: 18px;
  padding: 18px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1),
              0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

/* ???????? */



  20%, 100% {
    transform: translateX(100%) rotate(30deg);
  }
}

.profile-avatar-container {
  margin-right: 18px;
  position: relative;
  z-index: 2;
}

.profile-avatar {
  width: 84px;
  height: 84px;
  border: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 999px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  position: relative;
}

/* ???????? */



  to { transform: rotate(360deg); }
}

.profile-avatar:active {
  transform: scale(0.95);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  text-align: left;
  z-index: 2;
}

.profile-info__name {
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  margin-bottom: 6px;
  background: linear-gradient(120deg, #ffffff, #e0e7ff);


  background-clip: text;
  display: flex;
  align-items: center;
}

/* ??????? */



  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.profile-info__identity {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  background: linear-gradient(to right, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.25));
  padding: 5px 12px;
  border-radius: 12px;
  margin-top: 6px;
  display: inline-flex;
  align-items: center;
  border: 1px solid rgba(255, 255, 255, 0.3);

  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ?????? */


.profile-edit-btn {
  background: rgba(255, 255, 255, 0.2);

  padding: 10px 20px;
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12),
              0 2px 4px rgba(0, 0, 0, 0.1);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* ???????? */


/* ???????? */



  20%, 100% { left: 100%; }
}

.profile-edit-btn:active {
  transform: scale(0.95) translateY(2px);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.profile-edit-btn__text {
  font-size: 15px;
  color: #FFFFFF;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

/* ?????? */
.profile-info-card, .profile-function-card, .profile-motto-card {
  margin: 18px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 28px rgba(31, 60, 136, 0.07),
              0 4px 12px rgba(0, 0, 0, 0.04);
  position: relative;
  background: linear-gradient(145deg, #FFFFFF, #FCFCFF);
  border: 1px solid rgba(240, 243, 250, 0.8);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.profile-info-card:active, .profile-function-card:active, .profile-motto-card:active {
  transform: translateY(2px);
  box-shadow: 0 5px 15px rgba(31, 60, 136, 0.05);
}

/* ?????? */
.profile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  position: relative;
  z-index: 1;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
  background: linear-gradient(145deg, rgba(247, 250, 255, 0.5), rgba(255, 255, 255, 0.5));
}

.profile-card-body {
  background-color: #FFFFFF;
  position: relative;
  z-index: 1;
  padding: 8px 0;
}

/* ???? - ???? */
.profile-card-title {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  letter-spacing: 0.2px;
  position: relative;
  display: flex;
  align-items: center;
}



/* ??????*/
.profile-info-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px 18px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.profile-info-item:active {
  background-color: rgba(67, 97, 238, 0.03);
}

.profile-info-divider {
  height: 1px;
  background-color: rgba(240, 243, 250, 0.8);
  margin: 0 18px;
}

.profile-info-item__label {
  font-size: 15px;
  color: #5E6C84;
  font-weight: bold;
}

.profile-info-item__value {
  font-size: 15px;
  color: #2E3A59;
  font-weight: bold;
  background: linear-gradient(to right, rgba(91, 127, 255, 0.05), rgba(91, 127, 255, 0.1));
  padding: 6px 14px;
  border-radius: 12px;
  border: 1px solid rgba(91, 127, 255, 0.1);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.05);
}

/* ?????????? */
.profile-info-card::before, .profile-function-card::before,

/* ???????*/
.profile-motto-card {
  padding: 0;
  overflow: hidden;
}

.profile-motto-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  position: relative;
  z-index: 1;
  padding: 20px;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
  background: linear-gradient(145deg, rgba(247, 250, 255, 0.5), rgba(255, 255, 255, 0.5));
}

.profile-motto-title-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

/* ???????- ???? */
.profile-motto-header__title {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  letter-spacing: 0.2px;
  position: relative;
  display: flex;
  align-items: center;
}



.profile-motto-content {
  position: relative;
  z-index: 1;
  padding: 18px;
  background: linear-gradient(to bottom right,
              rgba(247, 250, 255, 0.5),
              rgba(255, 255, 255, 0.5));
}

.profile-motto-text {
  font-size: 15px;
  color: #5E6C84;
  line-height: 1.6;
  text-align: center;
  width: 100%;
  display: flex;
  background-color: rgba(91, 127, 255, 0.05);
  padding: 18px;
  border-radius: 14px;
  border: 1px solid rgba(91, 127, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
  position: relative;
  font-style: italic;
}





/* ????????*/
.profile-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  position: relative;
  overflow: hidden;
}

.profile-card:last-child {
  border-bottom: none;
}

.profile-card:active {
  background-color: rgba(67, 97, 238, 0.03);
  transform: translateY(1px);
}

/* ?????? */


.profile-card:

.profile-card__icon {
  width: 52px;
  height: 52px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  margin-right: 18px;
  box-shadow: 0 8px 20px rgba(31, 60, 136, 0.08),
              0 3px 8px rgba(0, 0, 0, 0.04);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}



.profile-card:active .profile-card__icon {
  transform: scale(0.95);
  box-shadow: 0 4px 10px rgba(31, 60, 136, 0.06);
}

.profile-card--password .profile-card__icon {
  background: linear-gradient(135deg, #4361ee, #3a86ff);
  color: #FFFFFF;
}

.profile-card--feedback .profile-card__icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  color: #FFFFFF;
}

.profile-card--about .profile-card__icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #FFFFFF;
}

.profile-card__content {
  flex: 1;
}

.profile-card__title {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  letter-spacing: 0.2px;
}

/* ?????? */
.profile-card__arrow {
  width: 36px;
  height: 36px;
  border-radius: 999px;
  background: linear-gradient(135deg, #f0f4ff, #ffffff);
  box-shadow: 0 3px 10px rgba(91, 127, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4361ee;
  font-size: 18px;
  margin-left: 10px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  border: 1px solid rgba(91, 127, 255, 0.15);
  position: relative;
}

/* ??????????*/


.profile-card:active .profile-card__arrow {
  transform: translateX(4px);
  background: linear-gradient(135deg, #eef2ff, #f7f9ff);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.15);
}

/* ???????*/
.profile-logout {
  width: calc(100% - 36px);
  margin: 30px 18px 32px;
  border-radius: 16px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, #ffffff, #fff5f5);
  box-shadow: 0 10px 25px rgba(255, 77, 79, 0.1),
              0 5px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(255, 77, 79, 0.15);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}





.profile-logout:active {
  transform: scale(0.98) translateY(2px);
  box-shadow: 0 5px 12px rgba(255, 77, 79, 0.08);
}

.profile-logout:

.profile-logout__text {
  color: #FF4D4F;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0.5px;
  margin-right: 20px;
}
</style>

