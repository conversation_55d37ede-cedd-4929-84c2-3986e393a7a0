<template>
  <view class="ai-toolbox">
    <scroll-view class="toolbox-scroll" scroll-y>
      <!-- ????? -->
      <view class="toolbox-section">
        <view class="section-header">
          <text class="section-title">????</text>
        </view>

        <!-- ???? -->
        <view class="tool-card" @click="navigateToTool('summary')">
          <view class="tool-content">
            <view class="tool-icon tool-icon--blue">
              <text class="tool-icon__text">??</text>
            </view>
            <view class="tool-info">
              <text class="tool-name">????</text>
              <text class="tool-desc">??????????</text>
            </view>
          </view>
          <text class="tool-arrow">?</text>
        </view>

        <!-- ??? -->
        <view class="tool-card" @click="navigateToTool('mistakes')">
          <view class="tool-content">
            <view class="tool-icon tool-icon--red">
              <text class="tool-icon__text">?</text>
            </view>
            <view class="tool-info">
              <text class="tool-name">???</text>
              <text class="tool-desc">???????????</text>
            </view>
          </view>
          <text class="tool-arrow">?</text>
        </view>

        <!-- ?? -->
        <view class="tool-card" @click="navigateToTool('translate')">
          <view class="tool-content">
            <view class="tool-icon tool-icon--green">
              <text class="tool-icon__text">??</text>
            </view>
            <view class="tool-info">
              <text class="tool-name">??</text>
              <text class="tool-desc">????????</text>
            </view>
          </view>
          <text class="tool-arrow">?</text>
        </view>
      </view>

      <!-- ???? -->
      <view class="toolbox-section">
        <view class="section-header">
          <text class="section-title">??</text>
        </view>

        <!-- ???? -->
        <view class="feature-card">
          <view class="feature-content">
            <view class="feature-text">
              <text class="feature-label">??</text>
              <text class="feature-title">AI????</text>
              <text class="feature-desc">???????????????????????</text>
            </view>
            <view class="feature-image">
              <text class="feature-image__icon">??</text>
            </view>
          </view>
          <view class="feature-button" @click="navigateToTool('plan')">
            <text class="feature-button__text">????</text>
          </view>
        </view>

        <!-- ???? -->
        <view class="tool-card" @click="navigateToTool('more')">
          <view class="tool-content">
            <view class="tool-icon tool-icon--purple">
              <text class="tool-icon__text">?</text>
            </view>
            <view class="tool-info">
              <text class="tool-name">????</text>
              <text class="tool-desc">????AI??</text>
            </view>
          </view>
          <text class="tool-arrow">?</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'AiToolbox',
  methods: {
    // ?????????
    navigateToTool(toolType) {
      // ?????????????
      switch(toolType) {
        case 'summary':
          uni.navigateTo({
            url: '/pages/ai/tools/summary/index',
            fail: (err) => {
              console.error('?????????:', err);
              uni.showToast({
                title: '????????',
                icon: 'none'
              });
            }
          });
          break;
        case 'mistakes':
          uni.navigateTo({
            url: '/pages/ai/tools/mistakes/index',
            fail: (err) => {
              console.error('????????:', err);
              uni.showToast({
                title: '????????',
                icon: 'none'
              });
            }
          });
          break;
        case 'translate':
          uni.navigateTo({
            url: '/pages/ai/tools/translate/index',
            fail: (err) => {
              console.error('???????:', err);
              uni.showToast({
                title: '????????',
                icon: 'none'
              });
            }
          });
          break;
        case 'plan':
          uni.navigateTo({
            url: '/pages/plan/ai-create',
            fail: (err) => {
              console.error('???AI??????:', err);
              uni.showToast({
                title: 'AI????????',
                icon: 'none'
              });
            }
          });
          break;
        case 'more':
          uni.showToast({
            title: '????????',
            icon: 'none'
          });
          break;
      }
    }
  }
}
</script>

<style>
.ai-toolbox {
  padding: 20px;
  background-color: #F8F9FA;
  min-height: 800px;
}

.toolbox-scroll {
  height: 700px;
}

.toolbox-section {
  margin-bottom: 30px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  position: relative;
  padding-left: 12px;
}

/* ?????? */
.tool-card {
  background-color: #FFFFFF;
  border-radius: 16px;
  margin-bottom: 12px;
  padding: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.tool-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tool-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}

.tool-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.tool-icon--blue {
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
}

.tool-icon--red {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
}

.tool-icon--green {
  background: linear-gradient(135deg, #34C759 0%, #5EE07A 100%);
}

.tool-icon--purple {
  background: linear-gradient(135deg, #AF52DE 0%, #D181FF 100%);
}

.tool-icon__text {
  font-size: 20px;
  color: #FFFFFF;
}

.tool-info {
  display: flex;
  flex-direction: column;
}

.tool-name {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4px;
}

.tool-desc {
  font-size: 14px;
  color: #666666;
}

.tool-arrow {
  font-size: 20px;
  color: #80B8F5;
  font-weight: normal;
}

/* ?????? */
.feature-card {
  background: linear-gradient(135deg, rgba(74, 123, 219, 0.1) 0%, rgba(128, 184, 245, 0.1) 100%);
  border-radius: 16px;
  margin-bottom: 16px;
  padding: 20px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(74, 123, 219, 0.1);
  border: 1px solid rgba(74, 123, 219, 0.2);
}

.feature-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 16px;
}

.feature-text {
  flex: 1;
}

.feature-label {
  display: flex;
  font-size: 12px;
  font-weight: bold;
  color: #FFFFFF;
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  padding: 4px 10px;
  border-radius: 12px;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(74, 123, 219, 0.2);
}

.feature-title {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
  display: flex;
}

.feature-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.feature-image {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.feature-image__icon {
  font-size: 30px;
}

.feature-button {
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  border-radius: 24px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(74, 123, 219, 0.2);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.feature-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 4px rgba(74, 123, 219, 0.1);
}

.feature-button__text {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: bold;
}
</style>

