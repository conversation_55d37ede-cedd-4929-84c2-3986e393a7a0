<template>
  <view class="summary-container">
    <!-- ????? -->
    <view class="summary-header">
      <view class="summary-back" @click="goBack">
        <text class="summary-back__icon">?</text>
      </view>
      <text class="summary-title">????</text>
    </view>

    <!-- ???? -->
    <scroll-view class="summary-content" scroll-y>
      <!-- ???? -->
      <view class="upload-area" @click="selectFile" v-if="!fileSelected && !summary">
        <view class="upload-icon">
          <text class="upload-icon__text">??</text>
        </view>
        <text class="upload-text">??????</text>
        <text class="upload-desc">??PDF?Word?TXT???</text>
      </view>

      <!-- ????? -->
      <view class="selected-file" v-if="fileSelected && !summary">
        <view class="file-info">
          <view class="file-icon">
            <text class="file-icon__text">??</text>
          </view>
          <view class="file-details">
            <text class="file-name">{{ fileName }}</text>
            <text class="file-size">{{ fileSize }}</text>
          </view>
        </view>

        <view class="file-actions">
          <view class="file-action file-action--delete" @click="removeFile">
            <text class="file-action__text">??</text>
          </view>
          <view class="file-action file-action--primary" @click="generateSummary">
            <text class="file-action__text">????</text>
          </view>
        </view>
      </view>

      <!-- ??? -->
      <view class="generating" v-if="isGenerating">
        <view class="generating-animation">
          <view class="generating-dot"></view>
          <view class="generating-dot"></view>
          <view class="generating-dot"></view>
        </view>
        <text class="generating-text">AI??????????...</text>
      </view>

      <!-- ???? -->
      <view class="summary-result" v-if="summary">
        <view class="summary-header-row">
          <text class="summary-header-text">????</text>
          <view class="summary-actions">
            <view class="summary-action" @click="copyToClipboard">
              <text class="summary-action__text">??</text>
            </view>
            <view class="summary-action" @click="resetSummary">
              <text class="summary-action__text">????</text>
            </view>
          </view>
        </view>

        <view class="summary-text-container">
          <text class="summary-text">{{ summary }}</text>
        </view>

        <view class="summary-keywords">
          <text class="summary-keywords__title">????</text>
          <view class="keyword-tags">
            <view class="keyword-tag" v-for="(keyword, index) in keywords" :key="index">
              <text class="keyword-tag__text">{{ keyword }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      fileSelected: false,
      fileName: '',
      fileSize: '',
      filePath: '',
      isGenerating: false,
      summary: '',
      keywords: []
    }
  },
  methods: {
    // ?????
    goBack() {
      uni.navigateBack();
    },

    // ????
    selectFile() {
      uni.chooseFile({
        count: 1,
        type: 'all',
        success: (res) => {
          const file = res.tempFiles[0];
          this.fileName = file.name;
          this.fileSize = this.formatFileSize(file.size);
          this.filePath = file.path;
          this.fileSelected = true;
        }
      });
    },

    // ????
    removeFile() {
      this.fileSelected = false;
      this.fileName = '';
      this.fileSize = '';
      this.filePath = '';
    },

    // ????
    generateSummary() {
      this.isGenerating = true;

      // ??API??
      setTimeout(() => {
        this.isGenerating = false;
        this.summary = '????????????????????????????AI??????????????????????????????\n\n???????AI????????????????AI????????????????????????????AI????????????????????????????????\n\n??????AI????????????????????????????????????????AI????????????AI???????????????????????????????';

        this.keywords = ['????', '????', '?????', '????', '?????'];
      }, 3000);
    },

    // ??????
    copyToClipboard() {
      uni.setClipboardData({
        data: this.summary,
        success: () => {
          uni.showToast({
            title: '????',
            icon: 'success'
          });
        }
      });
    },

    // ????
    resetSummary() {
      this.summary = '';
      this.keywords = [];
      this.fileSelected = false;
    },

    // ???????
    formatFileSize(size) {
      if (size < 1024) {
        return size + 'B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + 'KB';
      } else {
        return (size / (1024 * 1024)).toFixed(2) + 'MB';
      }
    }
  }
}
</script>

<style>
.summary-container {
  min-height: 800px;
  background-color: #F8F9FA;
  display: flex;
  flex-direction: column;
}

.summary-header {
  height: 56px;
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(74, 123, 219, 0.2);
  position: relative;
}

.summary-back {
  position: absolute;
  left: 16px;
  top: 0;
  height: 56px;
  display: flex;
  align-items: center;
}

.summary-back__icon {
  font-size: 18px;
  color: #FFFFFF;
  font-weight: bold;
}

.summary-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #FFFFFF;
}

.summary-content {
  flex: 1;
  padding: 16px;
}

/* ???? */
.upload-area {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.upload-icon {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background: linear-gradient(135deg, #E0EEFF 0%, #F0F7FF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(74, 123, 219, 0.15);
}

.upload-icon__text {
  font-size: 36px;
}

.upload-text {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
}

.upload-desc {
  font-size: 14px;
  color: #86868B;
}

/* ????? */
.selected-file {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.file-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
}

.file-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 4px 8px rgba(74, 123, 219, 0.2);
}

.file-icon__text {
  font-size: 24px;
  color: #FFFFFF;
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4px;
}

.file-size {
  font-size: 14px;
  color: #86868B;
}

.file-actions {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.file-action {
  padding: 10px 18px;
  border-radius: 24px;
  margin-left: 12px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.file-action:active {
  transform: scale(0.95);
}

.file-action--delete {
  background-color: #F0F0F0;
}

.file-action--primary {
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  box-shadow: 0 4px 8px rgba(74, 123, 219, 0.2);
}

.file-action__text {
  font-size: 14px;
  font-weight: bold;
  color: #666666;
}

.file-action--primary .file-action__text {
  color: #FFFFFF;
}

/* ??? */
.generating {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.generating-animation {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
}

.generating-dot {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  margin: 0 4px;
  animation: dot-bounce 1.4s infinite ease-in-out both;
  box-shadow: 0 2px 4px rgba(74, 123, 219, 0.2);
}

.generating-dot:nth-child(2) {
  animation-delay: 0.5s;
}

.generating-dot:nth-child(3) {
  animation-delay: 1s;
}

@keyframes dot-bounce {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.generating-text {
  font-size: 16px;
  color: #666666;
  font-weight: bold;
}

/* ???? */
.summary-result {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.summary-header-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.summary-header-text {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.summary-actions {
  display: flex;
  flex-direction: row;
}

.summary-action {
  padding: 8px 14px;
  border-radius: 20px;
  background-color: #F0F0F0;
  margin-left: 8px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.summary-action:active {
  transform: scale(0.95);
  background-color: #E0E0E0;
}

.summary-action__text {
  font-size: 12px;
  font-weight: bold;
  color: #4A7BDB;
}

.summary-text-container {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #F8F9FA;
  border-radius: 12px;
}

.summary-text {
  font-size: 16px;
  color: #333333;
  line-height: 1.6;
}

.summary-keywords {
  margin-top: 16px;
}

.summary-keywords__title {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
}

.keyword-tags {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.keyword-tag {
  padding: 8px 16px;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(74, 123, 219, 0.1) 0%, rgba(128, 184, 245, 0.1) 100%);
  border: 1px solid rgba(74, 123, 219, 0.2);
  margin-right: 8px;
  margin-bottom: 8px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.keyword-tag:active {
  transform: scale(0.95);
}

.keyword-tag__text {
  font-size: 12px;
  font-weight: bold;
  color: #4A6FE3;
}
</style>

