<template>
  <view class="history-container">
    <!-- 顶部导航栏 -->
    <sla-navbar title="计划历史记录" @back="goBack"></sla-navbar>

    <!-- 筛选区域 -->
    <view class="filter-section">
      <!-- 日期范围选择 -->
      <view class="date-range">
        <view class="date-picker">
          <text class="date-label">开始日期</text>
          <picker mode="date" :value="startDate" @change="onStartDateChange">
            <view class="picker-item">{{startDate || '请选择'}}</view>
          </picker>
        </view>
        <text class="date-separator">至</text>
        <view class="date-picker">
          <text class="date-label">结束日期</text>
          <picker mode="date" :value="endDate" @change="onEndDateChange">
            <view class="picker-item">{{endDate || '请选择'}}</view>
          </picker>
        </view>
      </view>

      <!-- 计划类型筛选 -->
      <view class="plan-type-filter">
        <text class="filter-label">计划类型</text>
        <view class="type-selector">
          <view
            class="type-item"
            :class="{'type-item--active': selectedType === 'all'}"
            @click="selectType('all')"
          >
            <text class="type-text">全部</text>
          </view>
          <view
            class="type-item"
            :class="{'type-item--active': selectedType === 'daily'}"
            @click="selectType('daily')"
          >
            <text class="type-text">日计划</text>
          </view>
          <view
            class="type-item"
            :class="{'type-item--active': selectedType === 'weekly'}"
            @click="selectType('weekly')"
          >
            <text class="type-text">周计划</text>
          </view>
          <view
            class="type-item"
            :class="{'type-item--active': selectedType === 'monthly'}"
            @click="selectType('monthly')"
          >
            <text class="type-text">月计划</text>
          </view>
          <view
            class="type-item"
            :class="{'type-item--active': selectedType === 'ai'}"
            @click="selectType('ai')"
          >
            <text class="type-text">AI计划</text>
          </view>
        </view>
      </view>

      <!-- 搜索按钮 -->
      <view class="search-button" @click="searchRecords">
        <text class="search-text">搜索</text>
      </view>
    </view>

    <!-- 统计区域 -->
    <view class="stats-section">
      <view class="stat-card">
        <text class="stat-value">{{checkInCount}}</text>
        <text class="stat-label">完成计划数</text>
      </view>
    </view>

    <!-- 历史记录列表 -->
    <scroll-view class="history-list" scroll-y>
      <view v-if="filteredRecords.length === 0" class="empty-state">
        <view class="empty-icon">📝</view>
        <text class="empty-title">暂无记录</text>
        <text class="empty-text">您在所选时间范围内没有完成的计划</text>
      </view>
      <view v-else class="plan-list">
        <view
          v-for="(record, index) in filteredRecords"
          :key="record.id || index"
          class="plan-row plan-row--completed"
        >
          <view class="plan-row__checkbox plan-row__checkbox--checked">
            <text class="plan-row__check-icon">✓</text>
          </view>
          <view class="plan-row__body">
            <view class="plan-row__header">
              <text class="plan-row__title">{{ record.title }}</text>
              <view class="plan-row__tags">
                <text class="plan-row__tag" :class="'plan-row__tag--' + record.priority">{{ getPriorityText(record.priority) }}</text>
                <text class="plan-row__tag plan-row__tag--type">{{ getTypeText(record.type) }}</text>
              </view>
            </view>
            <view class="plan-row__info">
              <text class="plan-row__date">{{ formatDate(record.date || record.startDate) }}</text>
            </view>
            <view class="plan-row__check-time">
              <text class="plan-row__check-time-label">完成时间：</text>
              <text class="plan-row__check-time-value">{{ formatDateTime(record.completionTime) }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import planApi from '../../utils/api/plan.js';

export default {
  components: {
    SlaNavbar
  },
  data() {
    return {
      startDate: '',
      endDate: '',
      selectedType: 'all',
      completedRecords: [] as Array<{
        id: number;
        planId: number;
        title: string;
        type: string;
        date: string;
        endDate: string;
        completionTime: string;
        isOnTime: boolean;
        description: string;
        aiGenerated: boolean;
        priority: string;
      }>,
      filteredRecords: [] as Array<{
        id: number;
        planId: number;
        title: string;
        type: string;
        date: string;
        endDate: string;
        completionTime: string;
        isOnTime: boolean;
        description: string;
        aiGenerated: boolean;
        priority: string;
      }>,
      checkInCount: 0
    }
  },
  onLoad() {
    // ????????30??    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    this.endDate = this.formatDateString(today);
    this.startDate = this.formatDateString(thirtyDaysAgo);

    // ????????????????????
    this.searchRecords();
  },
  methods: {
    // 返回上一页
    goBack() {
      // 确保隐藏任何可能的加载状态
      try {
        uni.hideLoading();
      } catch (e) {
        console.error('隐藏加载状态失败:', e);
      }

      // ????????????????????
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/plan/index',
          fail: (err) => {
            console.error('??????????', err);
            // ??switchTab???????reLaunch
            uni.reLaunch({
              url: '/pages/plan/index',
              fail: (err2) => {
                console.error('reLaunch????????', err2);
                // ??reLaunch?????????navigateBack
                uni.navigateBack();
              }
            });
          }
        });
      }, 100);
    },

    // 开始日期变化
    onStartDateChange(e) {
      this.startDate = e.detail.value;
    },

    // ??????
    onEndDateChange(e) {
      this.endDate = e.detail.value;
    },

    // ??????
    selectType(type) {
      this.selectedType = type;
    },

    // 搜索记录
    searchRecords() {
      try {
        // 确保隐藏任何加载状态
        try {
          uni.hideLoading();
        } catch (e) {
          console.error('隐藏加载失败', e);
        }

        if (!this.startDate || !this.endDate) {
          uni.showToast({
            title: '请选择日期范围',
            icon: 'none'
          });
          return;
        }

        // 显示加载
        uni.showLoading({
          title: '加载中..'
        });

        // ??????
        const params = {
          startDate: this.startDate,
          endDate: this.endDate,
          pageNum: 1,
          pageSize: 100 // ????????????????
        };

        // ???????"??"???????type??
        if (this.selectedType !== 'all') {
          params.type = this.selectedType;
        }

        // ??API??????
        planApi.getPlanHistory(params).then(res => {
          if (res.code === 200 && res.data) {
            // ??????
            const recordsList = res.data.records || [];

            // ??????
            this.completedRecords = recordsList.map((record) => {
              return {
                id: record.statusId,
                planId: record.planId,
                title: record.title,
                type: record.type,
                date: record.date || record.startDate, // ??date?startDate
                endDate: record.endDate || record.date, // ????????                completionTime: record.completionTime,
                isOnTime: record.isOnTime,
                description: record.description,
                aiGenerated: record.aiGenerated,
                priority: record.priority
              };
            });

            // ??????
            this.checkInCount = this.completedRecords.length;

            // ????
            this.filterRecords();

            console.log('????????:', this.completedRecords.length);
          } else {
            console.error('????????:', res.message);
            uni.showToast({
              title: '??????',
              icon: 'none'
            });
          }
        }).catch(err => {
          console.error('????????:', err);
          uni.showToast({
            title: '??????????',
            icon: 'none'
          });
        }).finally(() => {
          uni.hideLoading();
        });
      } catch (error) {
        console.error('searchRecords??????:', error);
        uni.hideLoading();
        uni.showToast({
          title: '????????',
          icon: 'none'
        });
      }
    },

    // ???????? (yyyy-MM-dd)
    formatDateString(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // ????????(yyyy?MM?dd??
    formatDate(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();

      return `${year}??{month}??{day}?`;
    },

    // ??????????(yyyy?MM?dd??HH:mm)
    formatDateTime(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}??{month}??{day}??${hours}:${minutes}`;
    },

    // ????????
    getTypeText(type) {
      const typeMap = {
        'daily': '????,
        'weekly': '????,
        'monthly': '????,
        'ai': '????',
        'custom': '??????
      };
      return typeMap[type] || '????';
    },

    // ????????
    getPlanTypeIcon(type) {
      const iconMap = {
        'daily': '??',
        'weekly': '??',
        'monthly': '??',
        'ai': '??',
        'custom': '??
      };
      return iconMap[type] || '??';
    },

    // ????????    getPriorityText(priority) {
      const priorityMap = {
        'high': '????',
        'medium': '????',
        'low': '????'
      };
      return priorityMap[priority] || '??????;
    },

    // ?????    filterRecords() {
      // ????????"???????????      if (this.selectedType === 'all') {
        this.filteredRecords = [...this.completedRecords];
        return;
      }

      // ??????????????      this.filteredRecords = this.completedRecords.filter(record => record.type === this.selectedType);
    }
  }
}
</script>

<style>
.history-container {
  min-height: 800px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom, #eef2ff, #f5f7fa);
}

/* ???????*/
.filter-section {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 16px;
  margin: 12px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);

}

.date-range {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;
}

.date-picker {
  flex: 1;
}

.date-label {
  font-size: 14px;
  color: #4A6FE3;
  margin-bottom: 6px;
  display: flex;
  font-weight: bold;
}

.picker-item {
  padding: 10px;
  border: 1px solid rgba(74, 111, 227, 0.2);
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background-color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.date-separator {
  color: #4A6FE3;
  font-weight: bold;
}

.plan-type-filter {
  margin-bottom: 16px;
}

.filter-label {
  font-size: 14px;
  color: #4A6FE3;
  margin-bottom: 6px;
  display: flex;
  font-weight: bold;
}

.type-selector {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
}

.type-item {
  padding: 6px 12px;
  background-color: rgba(245, 245, 245, 0.7);
  border-radius: 20px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.type-item--active {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 2px 8px rgba(91, 127, 255, 0.2);
}

.type-text {
  font-size: 13px;
  color: #666;
  font-weight: bold;
}

.type-item--active .type-text {
  color: #FFF;
}

.search-button {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  padding: 12px 0;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.2);
}

.search-text {
  font-size: 15px;
  color: #FFF;
  font-weight: bold;
}

/* ?????? */
.stats-section {
  margin: 0 12px 12px;
}

.stat-card {
  background-color: #FFF;
  border-radius: 16px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}



.stat-value {
  font-size: 32px;
  font-weight: bold;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);

  color: transparent;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: bold;
}

/* ?????? */
.history-list {
  flex: 1;
  padding: 0 12px 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  color: #4A6FE3;
  font-weight: bold;
  margin-bottom: 8px;
}

.empty-text {
  font-size: 14px;
  color: #888;
}

.plan-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* ???????? */
.plan-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 14px 16px;
  margin-bottom: 12px;
  border-radius: 16px;
  background-color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  border: 1px solid rgba(240, 240, 240, 0.8);
  position: relative;
  overflow: hidden;
}



.plan-row--completed {
  background-color: #FAFAFA;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  opacity: 0.85;
}



.plan-row__checkbox {
  width: 24px;
  height: 24px;
  border-radius: 999px;
  border: 2px solid #4CAF50;
  margin-right: 12px;
  flex-shrink: 0;
  position: relative;
  transition-property: transform, opacity; transition-duration: 0.25s; transition-timing-function: ease;
  background-color: #4CAF50;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.25);
}

.plan-row__check-icon {
  color: #FFFFFF;
  font-size: 14px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.plan-row__body {
  flex: 1;
  min-width: 0;
}

.plan-row__header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.plan-row__title {
  font-size: 15px;
  color: #2E3A59;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  padding-right: 5px;
}

.plan-row__tags {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
  margin-left: 8px;
  flex-shrink: 0;
}

.plan-row__tag {
  font-size: 11px;
  color: #FFFFFF;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  padding: 2px 8px;
  border-radius: 10px;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(91, 127, 255, 0.2);
}

.plan-row__tag--low {
  background: linear-gradient(135deg, #52C41A, #A0D911);
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
}

.plan-row__tag--medium {
  background: linear-gradient(135deg, #FAAD14, #FFC53D);
  box-shadow: 0 2px 4px rgba(250, 173, 20, 0.2);
}

.plan-row__tag--high {
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

.plan-row__tag--type {
  background: linear-gradient(135deg, #722ED1, #B37FEB);
  box-shadow: 0 2px 4px rgba(114, 46, 209, 0.2);
}

.plan-row__info {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 6px;
}

.plan-row__date {
  font-size: 12px;
  color: #4A6FE3;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-right: 8px;
  background-color: rgba(74, 111, 227, 0.08);
  padding: 2px 8px;
  border-radius: 12px;
}

.plan-row__check-time {
  display: flex;
  align-items: center;
  background-color: rgba(82, 196, 26, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  margin-top: 4px;
}

.plan-row__check-time-label {
  font-size: 12px;
  color: #666;
  margin-right: 4px;
}

.plan-row__check-time-value {
  font-size: 12px;
  color: #52C41A;
  font-weight: bold;
}
</style>

